# 应用配置
ENVIRONMENT=development
DEBUG=true
APP_NAME="Fire Command Dispatch System"
APP_VERSION="1.0.0"
SECRET_KEY="your-secret-key-here"

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=******************************************************************************************
DATABASE_SCHEMA=report_map
DATABASE_ECHO=false

# LangChain/LangGraph配置
OPENAI_API_KEY=your-openai-api-key
LANGCHAIN_TRACING_V2=false
LANGCHAIN_API_KEY=your-langchain-api-key
LANGCHAIN_PROJECT=fire-dispatch-system

# 高德地图API配置
AMAP_API_KEY=your-amap-api-key

# CORS配置
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://localhost:5173"]

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=1000
