# 应用配置
ENVIRONMENT=development
DEBUG=true
APP_NAME="Fire Command Dispatch System"
APP_VERSION="1.0.0"
SECRET_KEY="your-secret-key-here"

# 服务器配置
HOST=0.0.0.0
PORT=8000
WORKERS=1

# 数据库配置
DATABASE_URL=postgresql://fire_user:fire_password@localhost:5432/fire_dispatch
DATABASE_ECHO=false
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# LangChain/LangGraph配置
OPENAI_API_KEY=your-openai-api-key
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your-langchain-api-key
LANGCHAIN_PROJECT=fire-dispatch-system

# 高德地图API配置
AMAP_API_KEY=your-amap-api-key
AMAP_SECRET_KEY=your-amap-secret-key

# JWT配置
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["*"]
CORS_ALLOW_HEADERS=["*"]

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=1000

# 缓存配置
CACHE_TTL=3600
CACHE_PREFIX=fire_dispatch

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
