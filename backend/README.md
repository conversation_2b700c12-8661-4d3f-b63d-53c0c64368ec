# 🔥 灭火指挥调度地图Agent - Backend

基于FastAPI + LangGraph的智能消防指挥调度系统后端服务

## 🏗️ 架构概览

```
backend/
├── app/                    # 主应用目录
│   ├── __init__.py
│   ├── main.py            # FastAPI应用入口
│   ├── config/            # 配置管理
│   │   ├── __init__.py
│   │   └── settings.py    # 应用配置
│   ├── api/               # API路由层
│   │   ├── __init__.py
│   │   └── v1/            # API v1版本
│   │       ├── __init__.py
│   │       ├── fire_incidents.py  # 火情管理API
│   │       ├── resources.py       # 资源管理API
│   │       ├── dispatch.py        # 调度指令API
│   │       └── coordination.py    # 协调指挥API
│   ├── agents/            # LangGraph Agent系统
│   │   ├── __init__.py
│   │   ├── state.py       # Agent状态定义
│   │   ├── supervisor.py  # 中央调度Agent
│   │   └── graph.py       # Agent图构建
│   ├── models/            # 数据模型
│   │   ├── __init__.py
│   │   └── fire_station.py # 消防站模型
│   ├── services/          # 业务服务层
│   │   ├── __init__.py
│   │   └── fire_station_service.py
│   ├── database/          # 数据库相关
│   │   ├── __init__.py
│   │   └── connection.py  # 数据库连接
│   ├── websocket/         # WebSocket处理
│   │   ├── __init__.py
│   │   ├── manager.py     # 连接管理
│   │   └── handlers.py    # 消息处理
│   └── utils/             # 工具函数
│       ├── __init__.py
│       └── logger.py      # 日志配置
├── requirements.txt       # Python依赖
├── .env.example          # 环境变量示例
├── .gitignore           # Git忽略文件
├── start_server.py      # 启动脚本
├── test_api.py          # API测试脚本
└── explore_database.py  # 数据库探索工具
```

## 🚀 技术栈

### 核心框架
- **FastAPI** - 高性能异步Web框架
- **LangGraph** - AI Agent编排框架
- **AsyncPG** - PostgreSQL异步驱动

### 数据存储
- **PostgreSQL** - 主数据库（report_map模式）
- 支持PostGIS空间数据扩展

## 🛠️ 快速开始

### 环境要求
- Python 3.11+
- PostgreSQL 14+

### 本地开发

1. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

4. **启动开发服务器**
```bash
python start_server.py
# 或
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

5. **测试API**
```bash
python test_api.py
```

## 📡 API文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🔧 主要功能

### 1. 消防资源管理
- `/api/v1/resources/search` - 搜索周边消防资源
- `/api/v1/resources/` - 获取资源列表
- `/api/v1/resources/{id}` - 获取资源详情
- `/api/v1/resources/statistics/summary` - 获取统计信息

### 2. 火情事件管理
- `/api/v1/fire-incidents/` - 火情事件CRUD
- `/api/v1/fire-incidents/{id}/status` - 更新事件状态

### 3. 智能调度
- `/api/v1/dispatch/create` - 创建调度任务
- `/api/v1/dispatch/status/{session_id}` - 获取调度状态
- `/api/v1/dispatch/result/{session_id}` - 获取调度结果

### 4. 实时通信
- `/ws` - WebSocket连接端点

## 📊 数据库结构

基于现有PostgreSQL数据库的report_map模式：

- **fire_fulltime** - 专职消防站（39条记录）
- **fire_workstation** - 消防工作站（149条记录）
- **fire_workstation_quantity** - 工作站统计（745条记录）

## 🎯 LangGraph Agent系统

采用Supervisor架构的多Agent协作：

- **SupervisorAgent** - 中央调度器
- **SituationAgent** - 态势感知
- **ResourceAgent** - 资源调度
- **RouteAgent** - 路径规划
- **CoordinationAgent** - 协调指挥

## 🔍 开发工具

- `explore_database.py` - 数据库结构探索
- `test_api.py` - API功能测试
- `start_server.py` - 服务启动脚本

## 📝 配置说明

主要配置项（.env文件）：
```
DATABASE_URL=postgresql://username:password@host:port/database
DATABASE_SCHEMA=report_map
OPENAI_API_KEY=your-openai-api-key
AMAP_API_KEY=your-amap-api-key
```

## 🚀 部署

生产环境部署建议：
1. 使用Gunicorn + Uvicorn workers
2. 配置Nginx反向代理
3. 设置环境变量和日志
4. 配置监控和告警

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码变更
4. 编写测试用例
5. 提交Pull Request

## 📄 许可证

MIT License
