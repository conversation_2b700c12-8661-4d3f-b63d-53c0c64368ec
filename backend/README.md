# 🔥 灭火指挥调度地图Agent - Backend

基于FastAPI + LangGraph的智能消防指挥调度系统后端服务

## 🏗️ 架构概览

```
backend/
├── app/                    # 主应用目录
│   ├── __init__.py
│   ├── main.py            # FastAPI应用入口
│   ├── config/            # 配置管理
│   │   ├── __init__.py
│   │   └── settings.py    # 应用配置
│   ├── api/               # API路由层
│   │   ├── __init__.py
│   │   └── v1/            # API v1版本
│   │       ├── __init__.py
│   │       ├── fire_incidents.py  # 火情管理API
│   │       ├── resources.py       # 资源管理API
│   │       ├── dispatch.py        # 调度指令API
│   │       └── coordination.py    # 协调指挥API
│   ├── agents/            # LangGraph Agent系统
│   │   ├── __init__.py
│   │   ├── state.py       # Agent状态定义
│   │   ├── supervisor.py  # 中央调度Agent
│   │   └── graph.py       # Agent图构建
│   ├── models/            # 数据模型
│   │   ├── __init__.py
│   │   └── fire_station.py # 消防站模型
│   ├── services/          # 业务服务层
│   │   ├── __init__.py
│   │   └── fire_station_service.py
│   ├── database/          # 数据库相关
│   │   ├── __init__.py
│   │   └── connection.py  # 数据库连接
│   ├── websocket/         # WebSocket处理
│   │   ├── __init__.py
│   │   ├── manager.py     # 连接管理
│   │   └── handlers.py    # 消息处理
│   └── utils/             # 工具函数
│       ├── __init__.py
│       └── logger.py      # 日志配置
├── requirements.txt       # Python依赖
├── .env.example          # 环境变量示例
├── .gitignore           # Git忽略文件
├── start_server.py      # 启动脚本
├── test_api.py          # API测试脚本
└── README.md            # 项目说明
```

## 🚀 技术栈

### 核心框架
- **FastAPI** - 高性能异步Web框架
- **LangGraph** - AI Agent编排框架
- **AsyncPG** - PostgreSQL异步驱动

### 数据存储
- **PostgreSQL** - 主数据库（现有数据库：integrated_service_governance）
- **Schema**: report_map

### 现有数据表
- **fire_fulltime** - 专职消防站（39条记录）
- **fire_workstation** - 消防工作站（149条记录）
- **fire_workstation_quantity** - 工作站统计（745条记录）

## 🛠️ 快速开始

### 环境要求
- Python 3.11+
- PostgreSQL 数据库访问权限

### 本地开发

1. **安装依赖**
```bash
cd backend
pip install -r requirements.txt
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接
```

3. **启动开发服务器**
```bash
python start_server.py
# 或
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

4. **测试API**
```bash
python test_api.py
```

## 📡 API文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🎯 核心功能

### 1. 消防资源管理API
- `POST /api/v1/resources/search` - 搜索周边消防站
- `GET /api/v1/resources/` - 获取消防站列表
- `GET /api/v1/resources/{id}` - 获取消防站详情
- `GET /api/v1/resources/statistics/summary` - 获取统计信息

### 2. LangGraph Agent系统
- **SupervisorAgent** - 中央调度器
- **多Agent协作** - 态势感知、资源调度、路径规划、协调指挥

### 3. 实时通信
- WebSocket支持实时状态更新
- 多用户协同操作

## 🔧 数据库配置

当前连接配置：
```
Host: *************
Port: 18640
Database: integrated_service_governance
Schema: report_map
Username: zhaoyingnan
```

## 📊 数据结构

### fire_fulltime (专职消防站)
- 基本信息：id, name, address, dadui
- 位置信息：horizontal (经度), vertical (纬度)
- 状态信息：statue, 执勤状态等

### fire_workstation (消防工作站)
- 基本信息：id, name, address, region, jiedao
- 位置信息：horizontal (经度), vertical (纬度)
- 人员配置：规定人数, 工作人员人数等

## 🧪 测试

```bash
# 运行API测试
python test_api.py

# 测试数据库连接
python explore_database.py
```

## 🚀 部署

生产环境建议：
1. 使用环境变量管理敏感配置
2. 配置日志收集
3. 设置监控和告警
4. 使用反向代理（Nginx）

## 🤝 开发指南

1. 遵循FastAPI最佳实践
2. 使用Pydantic进行数据验证
3. 结构化日志记录
4. 异步编程模式

## 📄 许可证

MIT License
