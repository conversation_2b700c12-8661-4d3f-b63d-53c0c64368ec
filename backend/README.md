# 🔥 灭火指挥调度地图Agent - Backend

基于FastAPI + LangGraph的智能消防指挥调度系统后端服务

## 🏗️ 架构概览

```
backend/
├── app/                    # 主应用目录
│   ├── __init__.py
│   ├── main.py            # FastAPI应用入口
│   ├── config/            # 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py    # 应用配置
│   │   └── database.py    # 数据库配置
│   ├── api/               # API路由层
│   │   ├── __init__.py
│   │   ├── v1/            # API v1版本
│   │   │   ├── __init__.py
│   │   │   ├── fire_incidents.py  # 火情管理API
│   │   │   ├── resources.py       # 资源管理API
│   │   │   ├── dispatch.py        # 调度指令API
│   │   │   └── coordination.py    # 协调指挥API
│   │   └── deps.py        # API依赖注入
│   ├── agents/            # LangGraph Agent系统
│   │   ├── __init__.py
│   │   ├── state.py       # Agent状态定义
│   │   ├── supervisor.py  # 中央调度Agent
│   │   ├── situation.py   # 态势感知Agent
│   │   ├── resource.py    # 资源调度Agent
│   │   ├── route.py       # 路径规划Agent
│   │   ├── coordination.py # 协调指挥Agent
│   │   └── graph.py       # Agent图构建
│   ├── models/            # 数据模型
│   │   ├── __init__.py
│   │   ├── base.py        # 基础模型
│   │   ├── fire_incident.py # 火情模型
│   │   ├── resource.py    # 资源模型
│   │   ├── dispatch.py    # 调度模型
│   │   └── user.py        # 用户模型
│   ├── schemas/           # Pydantic模式
│   │   ├── __init__.py
│   │   ├── fire_incident.py
│   │   ├── resource.py
│   │   ├── dispatch.py
│   │   └── common.py
│   ├── services/          # 业务服务层
│   │   ├── __init__.py
│   │   ├── fire_incident.py
│   │   ├── resource.py
│   │   ├── dispatch.py    # 集成LangGraph调度服务
│   │   └── coordination.py
│   ├── database/          # 数据库相关
│   │   ├── __init__.py
│   │   ├── connection.py  # 数据库连接
│   │   ├── session.py     # 会话管理
│   │   └── migrations/    # 数据库迁移
│   ├── websocket/         # WebSocket处理
│   │   ├── __init__.py
│   │   ├── manager.py     # 连接管理
│   │   └── handlers.py    # 消息处理
│   └── utils/             # 工具函数
│       ├── __init__.py
│       ├── logger.py      # 日志配置
│       ├── security.py    # 安全工具
│       └── spatial.py     # 空间计算工具
├── tests/                 # 测试文件
│   ├── __init__.py
│   ├── conftest.py       # 测试配置
│   ├── test_api/         # API测试
│   ├── test_agents/      # Agent测试
│   └── test_services/    # 服务测试
├── requirements.txt       # Python依赖
├── requirements-dev.txt   # 开发依赖
├── docker-compose.yml     # Docker开发环境
├── Dockerfile            # Docker镜像
├── .env.example          # 环境变量示例
├── .gitignore           # Git忽略文件
└── README.md            # 项目说明
```

## 🚀 技术栈

### 核心框架
- **FastAPI** - 高性能异步Web框架
- **LangGraph** - AI Agent编排框架
- **LangChain** - LLM集成框架
- **SQLAlchemy** - Python ORM
- **Pydantic** - 数据验证和序列化

### 数据存储
- **PostgreSQL** - 主数据库（计划升级PostGIS）
- **Redis** - 缓存和会话存储

### 开发工具
- **Uvicorn** - ASGI服务器
- **Pytest** - 测试框架
- **Black** - 代码格式化
- **Flake8** - 代码检查
- **MyPy** - 类型检查

## 🎯 核心功能

### 1. LangGraph Agent系统
采用**Supervisor架构**的多Agent协作系统：

- **SupervisorAgent** - 中央调度器，智能路由决策
- **SituationAgent** - 态势感知，分析火情和环境
- **ResourceAgent** - 资源调度，匹配最优消防资源
- **RouteAgent** - 路径规划，计算最佳救援路径
- **CoordinationAgent** - 协调指挥，处理多部门协作

### 2. RESTful API
- `/api/v1/fire-incidents` - 火情上报和管理
- `/api/v1/resources` - 消防资源查询和管理
- `/api/v1/dispatch` - 智能调度指令
- `/api/v1/coordination` - 协调指挥功能

### 3. 实时通信
- WebSocket支持实时状态更新
- 多用户协同操作
- 调度指令实时推送

### 4. 数据管理
- PostgreSQL存储业务数据
- 支持空间数据查询（PostGIS升级预留）
- Redis缓存热点数据

## 🛠️ 快速开始

### 环境要求
- Python 3.11+
- PostgreSQL 14+
- Redis 6+
- Docker & Docker Compose（可选）

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd backend
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

5. **启动数据库服务**
```bash
docker-compose up -d postgres redis
```

6. **运行数据库迁移**
```bash
alembic upgrade head
```

7. **启动开发服务器**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Docker部署

```bash
# 构建并启动所有服务
docker-compose up --build

# 后台运行
docker-compose up -d
```

## 📡 API文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_agents/

# 生成覆盖率报告
pytest --cov=app tests/
```

## 🔧 开发指南

### 代码规范
```bash
# 格式化代码
black app/ tests/

# 检查代码质量
flake8 app/ tests/

# 类型检查
mypy app/
```

### Agent开发
1. 在 `app/agents/` 目录下创建新的Agent
2. 继承基础Agent类，实现核心逻辑
3. 在 `app/agents/graph.py` 中注册Agent
4. 编写对应的测试用例

### API开发
1. 在 `app/api/v1/` 目录下创建路由文件
2. 定义Pydantic模式在 `app/schemas/`
3. 实现业务逻辑在 `app/services/`
4. 编写API测试用例

## 🚀 部署

### 生产环境配置
1. 设置环境变量 `ENVIRONMENT=production`
2. 配置生产数据库连接
3. 设置Redis集群
4. 配置日志收集
5. 设置监控和告警

### 性能优化
- 使用连接池优化数据库访问
- Redis缓存热点数据
- 异步处理长时间任务
- 负载均衡和水平扩展

## 📊 监控

- 健康检查: `/health`
- 指标监控: `/metrics`
- 日志级别可配置
- 支持结构化日志输出

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码变更
4. 编写测试用例
5. 提交Pull Request

## 📄 许可证

MIT License

## 🆘 支持

如有问题，请提交Issue或联系开发团队。
