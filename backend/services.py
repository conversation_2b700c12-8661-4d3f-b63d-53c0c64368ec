"""
业务服务层

提供消防站相关的数据查询服务
"""

import math
from typing import List, Optional, Dict, Any
import structlog

from database import db_manager
from models import (
    FireFulltimeStation, 
    FireWorkstation, 
    FireStationLocation,
    FireStationSearchRequest,
    FireStationSearchResponse
)

logger = structlog.get_logger(__name__)


class FireStationService:
    """消防站数据服务"""
    
    def __init__(self):
        self.db = db_manager
    
    def calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """
        计算两点间距离（米）
        使用Haversine公式
        """
        # 将度转换为弧度
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        # Haversine公式
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        # 地球半径（米）
        r = 6371000
        
        return c * r
    
    async def get_fulltime_stations(self) -> List[FireFulltimeStation]:
        """获取所有专职消防站"""
        query = f"""
        SELECT * FROM {self.db.get_table_name('fire_fulltime')}
        ORDER BY rank
        """
        
        try:
            rows = await self.db.execute_query(query)
            stations = []
            
            for row in rows:
                station = FireFulltimeStation(**row)
                stations.append(station)
            
            logger.info(f"Retrieved {len(stations)} fulltime fire stations")
            return stations
            
        except Exception as e:
            logger.error("Failed to get fulltime stations", error=str(e))
            raise
    
    async def get_workstations(self) -> List[FireWorkstation]:
        """获取所有消防工作站"""
        query = f"""
        SELECT * FROM {self.db.get_table_name('fire_workstation')}
        ORDER BY rank
        """
        
        try:
            rows = await self.db.execute_query(query)
            stations = []
            
            for row in rows:
                station = FireWorkstation(**row)
                stations.append(station)
            
            logger.info(f"Retrieved {len(stations)} fire workstations")
            return stations
            
        except Exception as e:
            logger.error("Failed to get workstations", error=str(e))
            raise
    
    async def search_nearby_stations(self, request: FireStationSearchRequest) -> FireStationSearchResponse:
        """搜索附近的消防站"""
        try:
            all_stations = []
            
            # 获取专职消防站
            if not request.station_types or 'fulltime' in request.station_types:
                fulltime_stations = await self.get_fulltime_stations()
                for station in fulltime_stations:
                    if station.horizontal is not None and station.vertical is not None:
                        # 计算距离
                        distance = self.calculate_distance(
                            request.latitude, request.longitude,
                            station.vertical, station.horizontal  # 注意：数据库中vertical是纬度，horizontal是经度
                        )
                        
                        if distance <= request.radius:
                            # 应用筛选条件
                            if request.dadui_filter and station.dadui != request.dadui_filter:
                                continue
                            if request.status_filter and station.statue != request.status_filter:
                                continue
                            
                            location = FireStationLocation(
                                id=station.id,
                                name=station.name or "未知专职消防站",
                                address=station.address,
                                latitude=station.vertical,
                                longitude=station.horizontal,
                                station_type="fulltime",
                                dadui=station.dadui,
                                status=station.statue,
                                img_url=station.img_url
                            )
                            all_stations.append((location, distance))
            
            # 获取消防工作站
            if not request.station_types or 'workstation' in request.station_types:
                workstations = await self.get_workstations()
                for station in workstations:
                    if station.horizontal is not None and station.vertical is not None:
                        # 计算距离
                        distance = self.calculate_distance(
                            request.latitude, request.longitude,
                            station.vertical, station.horizontal
                        )
                        
                        if distance <= request.radius:
                            # 应用筛选条件
                            if request.dadui_filter and station.dadui != request.dadui_filter:
                                continue
                            
                            location = FireStationLocation(
                                id=str(station.id),
                                name=station.name or "未知消防工作站",
                                address=station.address,
                                latitude=station.vertical,
                                longitude=station.horizontal,
                                station_type="workstation",
                                dadui=station.dadui,
                                status="active",  # 工作站默认为活跃状态
                                img_url=station.img_url
                            )
                            all_stations.append((location, distance))
            
            # 按距离排序
            all_stations.sort(key=lambda x: x[1])
            
            # 提取站点信息
            stations = [station for station, distance in all_stations]
            
            response = FireStationSearchResponse(
                total_count=len(stations),
                stations=stations,
                search_center={
                    "latitude": request.latitude,
                    "longitude": request.longitude
                },
                search_radius=request.radius
            )
            
            logger.info(
                "Station search completed",
                center_lat=request.latitude,
                center_lon=request.longitude,
                radius=request.radius,
                found_count=len(stations)
            )
            
            return response
            
        except Exception as e:
            logger.error("Failed to search nearby stations", error=str(e))
            raise
    
    async def get_station_by_id(self, station_id: str, station_type: str = None) -> Optional[FireStationLocation]:
        """根据ID获取消防站信息"""
        try:
            # 如果指定了类型，只查询对应类型
            if station_type == "fulltime":
                return await self._get_fulltime_station_by_id(station_id)
            elif station_type == "workstation":
                return await self._get_workstation_by_id(station_id)
            else:
                # 尝试两种类型
                station = await self._get_fulltime_station_by_id(station_id)
                if not station:
                    station = await self._get_workstation_by_id(station_id)
                return station
                
        except Exception as e:
            logger.error(f"Failed to get station by id {station_id}", error=str(e))
            raise
    
    async def _get_fulltime_station_by_id(self, station_id: str) -> Optional[FireStationLocation]:
        """获取专职消防站"""
        query = f"""
        SELECT * FROM {self.db.get_table_name('fire_fulltime')}
        WHERE id = $1
        """
        
        row = await self.db.execute_query_one(query, station_id)
        if not row:
            return None
        
        return FireStationLocation(
            id=row['id'],
            name=row['name'] or "未知专职消防站",
            address=row['address'],
            latitude=row['vertical'],
            longitude=row['horizontal'],
            station_type="fulltime",
            dadui=row['dadui'],
            status=row['statue'],
            img_url=row['img_url']
        )
    
    async def _get_workstation_by_id(self, station_id: str) -> Optional[FireStationLocation]:
        """获取消防工作站"""
        query = f"""
        SELECT * FROM {self.db.get_table_name('fire_workstation')}
        WHERE id = $1
        """
        
        try:
            row = await self.db.execute_query_one(query, int(station_id))
        except ValueError:
            return None
        
        if not row:
            return None
        
        return FireStationLocation(
            id=str(row['id']),
            name=row['name'] or "未知消防工作站",
            address=row['address'],
            latitude=row['vertical'],
            longitude=row['horizontal'],
            station_type="workstation",
            dadui=row['dadui'],
            status="active",
            img_url=row['img_url']
        )
    
    async def get_dadui_list(self) -> List[str]:
        """获取所有大队列表"""
        query = f"""
        SELECT DISTINCT dadui FROM {self.db.get_table_name('fire_fulltime')}
        WHERE dadui IS NOT NULL
        UNION
        SELECT DISTINCT dadui FROM {self.db.get_table_name('fire_workstation')}
        WHERE dadui IS NOT NULL
        ORDER BY dadui
        """
        
        try:
            rows = await self.db.execute_query(query)
            dadui_list = [row['dadui'] for row in rows if row['dadui']]
            
            logger.info(f"Retrieved {len(dadui_list)} dadui units")
            return dadui_list
            
        except Exception as e:
            logger.error("Failed to get dadui list", error=str(e))
            raise
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取消防站统计信息"""
        try:
            # 专职消防站统计
            fulltime_query = f"""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN statue = '已完成' THEN 1 END) as completed,
                COUNT(CASE WHEN statue IS NULL OR statue = '' THEN 1 END) as pending,
                COUNT(DISTINCT dadui) as dadui_count
            FROM {self.db.get_table_name('fire_fulltime')}
            """
            
            # 消防工作站统计
            workstation_query = f"""
            SELECT 
                COUNT(*) as total,
                COUNT(DISTINCT region) as region_count,
                COUNT(DISTINCT dadui) as dadui_count
            FROM {self.db.get_table_name('fire_workstation')}
            """
            
            fulltime_stats = await self.db.execute_query_one(fulltime_query)
            workstation_stats = await self.db.execute_query_one(workstation_query)
            
            statistics = {
                "fulltime_stations": {
                    "total": fulltime_stats['total'],
                    "completed": fulltime_stats['completed'],
                    "pending": fulltime_stats['pending'],
                    "dadui_count": fulltime_stats['dadui_count']
                },
                "workstations": {
                    "total": workstation_stats['total'],
                    "region_count": workstation_stats['region_count'],
                    "dadui_count": workstation_stats['dadui_count']
                },
                "total_stations": fulltime_stats['total'] + workstation_stats['total']
            }
            
            logger.info("Retrieved fire station statistics", statistics=statistics)
            return statistics
            
        except Exception as e:
            logger.error("Failed to get statistics", error=str(e))
            raise


# 创建全局服务实例
fire_station_service = FireStationService()
