"""
FastAPI应用主入口

集成LangGraph Agent系统的消防指挥调度API服务
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import structlog
import time

from config import get_settings
from logger import setup_logging
from database import init_database, close_database

# 设置日志
setup_logging()
logger = structlog.get_logger(__name__)

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("Starting Fire Command Dispatch System", version=settings.APP_VERSION)
    
    try:
        # 初始化数据库连接
        await init_database()
        logger.info("Database initialized successfully")
        
        logger.info("Application startup complete")
        
    except Exception as e:
        logger.error("Failed to initialize application", error=str(e))
        raise
    
    yield
    
    # 关闭时执行
    logger.info("Shutting down Fire Command Dispatch System")
    
    try:
        # 关闭数据库连接
        await close_database()
        
        logger.info("Application shutdown complete")
        
    except Exception as e:
        logger.error("Error during shutdown", error=str(e))


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.APP_NAME,
    description="基于LangGraph的智能消防指挥调度系统API",
    version=settings.APP_VERSION,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    openapi_url="/openapi.json" if settings.DEBUG else None,
    lifespan=lifespan,
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """添加请求处理时间头"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录请求日志"""
    start_time = time.time()
    
    # 记录请求开始
    logger.info(
        "Request started",
        method=request.method,
        url=str(request.url),
        client_ip=request.client.host if request.client else None,
    )
    
    response = await call_next(request)
    
    # 记录请求完成
    process_time = time.time() - start_time
    logger.info(
        "Request completed",
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        process_time=process_time,
    )
    
    return response


# 异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(
        "Unhandled exception",
        exc_info=exc,
        method=request.method,
        url=str(request.url),
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
        }
    )


# 健康检查端点
@app.get("/health", tags=["Health"])
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "environment": settings.ENVIRONMENT,
    }


@app.get("/", tags=["Root"])
async def root():
    """根端点"""
    return {
        "message": f"Welcome to {settings.APP_NAME}",
        "version": settings.APP_VERSION,
        "docs": "/docs",
        "health": "/health",
    }


# 注册API路由
from api import resources, fire_incidents, dispatch, coordination

app.include_router(
    resources.router,
    prefix="/api/v1/resources",
    tags=["Resources"]
)

app.include_router(
    fire_incidents.router,
    prefix="/api/v1/fire-incidents",
    tags=["Fire Incidents"]
)

app.include_router(
    dispatch.router,
    prefix="/api/v1/dispatch",
    tags=["Dispatch"]
)

app.include_router(
    coordination.router,
    prefix="/api/v1/coordination",
    tags=["Coordination"]
)


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_config=None,  # 使用自定义日志配置
    )
