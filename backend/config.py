"""
应用配置管理

使用Pydantic Settings进行配置管理，支持环境变量和.env文件
"""

from functools import lru_cache
from typing import List
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = Field(default="Fire Command Dispatch System", description="应用名称")
    APP_VERSION: str = Field(default="1.0.0", description="应用版本")
    ENVIRONMENT: str = Field(default="development", description="运行环境")
    DEBUG: bool = Field(default=True, description="调试模式")
    SECRET_KEY: str = Field(default="dev-secret-key", description="应用密钥")
    
    # 服务器配置
    HOST: str = Field(default="0.0.0.0", description="服务器主机")
    PORT: int = Field(default=8000, description="服务器端口")
    
    # 数据库配置
    DATABASE_URL: str = Field(description="数据库连接URL")
    DATABASE_SCHEMA: str = Field(default="report_map", description="数据库模式")
    DATABASE_ECHO: bool = Field(default=False, description="数据库SQL日志")
    
    # LangChain/LangGraph配置
    OPENAI_API_KEY: str = Field(default="", description="OpenAI API密钥")
    LANGCHAIN_TRACING_V2: bool = Field(default=False, description="LangChain追踪")
    LANGCHAIN_API_KEY: str = Field(default="", description="LangChain API密钥")
    LANGCHAIN_PROJECT: str = Field(default="fire-dispatch-system", description="LangChain项目名")
    
    # 高德地图API配置
    AMAP_API_KEY: str = Field(default="", description="高德地图API密钥")
    
    # CORS配置
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080", "http://localhost:5173"],
        description="允许的跨域源"
    )
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FORMAT: str = Field(default="json", description="日志格式")
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = Field(default=30, description="WebSocket心跳间隔（秒）")
    WS_MAX_CONNECTIONS: int = Field(default=1000, description="WebSocket最大连接数")
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.ENVIRONMENT == "development"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.ENVIRONMENT == "production"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    return Settings()
