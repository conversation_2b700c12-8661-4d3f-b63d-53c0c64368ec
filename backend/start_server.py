"""
服务启动脚本

用于启动消防指挥调度系统后端服务
"""

import os
import sys
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """启动服务"""
    print("🔥 启动消防指挥调度系统后端服务")
    print("=" * 50)
    
    # 检查环境变量文件
    env_file = project_root / ".env"
    if not env_file.exists():
        print("⚠️  未找到.env文件，将使用默认配置")
        print("   建议复制.env.example为.env并配置相关参数")
    else:
        print("✅ 找到.env配置文件")
    
    # 启动配置
    config = {
        "app": "main:app",
        "host": "0.0.0.0",
        "port": 8000,
        "reload": True,
        "reload_dirs": [str(project_root / "app")],
        "log_level": "info"
    }
    
    print(f"🚀 启动服务...")
    print(f"   地址: http://{config['host']}:{config['port']}")
    print(f"   API文档: http://{config['host']}:{config['port']}/docs")
    print(f"   重载模式: {'开启' if config['reload'] else '关闭'}")
    print("=" * 50)
    
    try:
        uvicorn.run(**config)
    except KeyboardInterrupt:
        print("\n⏹️  服务已停止")
    except Exception as e:
        print(f"\n💥 服务启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
