"""
应用配置管理

使用Pydantic Settings进行配置管理，支持环境变量和.env文件
"""

from functools import lru_cache
from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = Field(default="Fire Command Dispatch System", description="应用名称")
    APP_VERSION: str = Field(default="1.0.0", description="应用版本")
    ENVIRONMENT: str = Field(default="development", description="运行环境")
    DEBUG: bool = Field(default=True, description="调试模式")
    SECRET_KEY: str = Field(default="dev-secret-key", description="应用密钥")
    
    # 服务器配置
    HOST: str = Field(default="0.0.0.0", description="服务器主机")
    PORT: int = Field(default=8000, description="服务器端口")
    
    # 数据库配置
    DATABASE_URL: str = Field(
        default="******************************************************************************************",
        description="数据库连接URL"
    )
    DATABASE_SCHEMA: str = Field(default="report_map", description="数据库模式")
    DATABASE_ECHO: bool = Field(default=False, description="数据库SQL日志")
    DATABASE_POOL_SIZE: int = Field(default=10, description="数据库连接池大小")
    DATABASE_MAX_OVERFLOW: int = Field(default=20, description="数据库连接池最大溢出")
    
    # LangChain/LangGraph配置
    OPENAI_API_KEY: str = Field(default="", description="OpenAI API密钥")
    LANGCHAIN_TRACING_V2: bool = Field(default=False, description="LangChain追踪")
    LANGCHAIN_API_KEY: Optional[str] = Field(default=None, description="LangChain API密钥")
    LANGCHAIN_PROJECT: str = Field(default="fire-dispatch-system", description="LangChain项目名")
    
    # 高德地图API配置
    AMAP_API_KEY: str = Field(default="", description="高德地图API密钥")
    
    # CORS配置
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="允许的跨域源"
    )
    CORS_ALLOW_CREDENTIALS: bool = Field(default=True, description="允许跨域凭证")
    CORS_ALLOW_METHODS: List[str] = Field(default=["*"], description="允许的HTTP方法")
    CORS_ALLOW_HEADERS: List[str] = Field(default=["*"], description="允许的HTTP头")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FORMAT: str = Field(default="json", description="日志格式")
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = Field(default=30, description="WebSocket心跳间隔（秒）")
    WS_MAX_CONNECTIONS: int = Field(default=1000, description="WebSocket最大连接数")
    
    @validator("ENVIRONMENT")
    def validate_environment(cls, v):
        """验证环境配置"""
        allowed_envs = ["development", "testing", "production"]
        if v not in allowed_envs:
            raise ValueError(f"Environment must be one of {allowed_envs}")
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        """验证日志级别"""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"Log level must be one of {allowed_levels}")
        return v.upper()
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.ENVIRONMENT == "development"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.ENVIRONMENT == "production"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    return Settings()
