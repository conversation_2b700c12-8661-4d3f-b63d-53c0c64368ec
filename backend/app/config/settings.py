"""
应用配置管理

使用Pydantic Settings进行配置管理，支持环境变量和.env文件
"""

from functools import lru_cache
from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = Field(default="Fire Command Dispatch System", description="应用名称")
    APP_VERSION: str = Field(default="1.0.0", description="应用版本")
    ENVIRONMENT: str = Field(default="development", description="运行环境")
    DEBUG: bool = Field(default=True, description="调试模式")
    SECRET_KEY: str = Field(description="应用密钥")
    
    # 服务器配置
    HOST: str = Field(default="0.0.0.0", description="服务器主机")
    PORT: int = Field(default=8000, description="服务器端口")
    WORKERS: int = Field(default=1, description="工作进程数")
    
    # 数据库配置
    DATABASE_URL: str = Field(description="数据库连接URL")
    DATABASE_ECHO: bool = Field(default=False, description="数据库SQL日志")
    DATABASE_POOL_SIZE: int = Field(default=10, description="数据库连接池大小")
    DATABASE_MAX_OVERFLOW: int = Field(default=20, description="数据库连接池最大溢出")
    
    # Redis配置
    REDIS_URL: str = Field(default="redis://localhost:6379/0", description="Redis连接URL")
    REDIS_PASSWORD: Optional[str] = Field(default=None, description="Redis密码")
    REDIS_DB: int = Field(default=0, description="Redis数据库编号")
    
    # LangChain/LangGraph配置
    OPENAI_API_KEY: str = Field(description="OpenAI API密钥")
    LANGCHAIN_TRACING_V2: bool = Field(default=False, description="LangChain追踪")
    LANGCHAIN_API_KEY: Optional[str] = Field(default=None, description="LangChain API密钥")
    LANGCHAIN_PROJECT: str = Field(default="fire-dispatch-system", description="LangChain项目名")
    
    # 高德地图API配置
    AMAP_API_KEY: str = Field(description="高德地图API密钥")
    AMAP_SECRET_KEY: Optional[str] = Field(default=None, description="高德地图密钥")
    
    # JWT配置
    JWT_SECRET_KEY: str = Field(description="JWT密钥")
    JWT_ALGORITHM: str = Field(default="HS256", description="JWT算法")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="JWT过期时间（分钟）")
    
    # CORS配置
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="允许的跨域源"
    )
    CORS_ALLOW_CREDENTIALS: bool = Field(default=True, description="允许跨域凭证")
    CORS_ALLOW_METHODS: List[str] = Field(default=["*"], description="允许的HTTP方法")
    CORS_ALLOW_HEADERS: List[str] = Field(default=["*"], description="允许的HTTP头")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FORMAT: str = Field(default="json", description="日志格式")
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = Field(default=30, description="WebSocket心跳间隔（秒）")
    WS_MAX_CONNECTIONS: int = Field(default=1000, description="WebSocket最大连接数")
    
    # 缓存配置
    CACHE_TTL: int = Field(default=3600, description="缓存过期时间（秒）")
    CACHE_PREFIX: str = Field(default="fire_dispatch", description="缓存键前缀")
    
    # 文件上传配置
    MAX_FILE_SIZE: int = Field(default=10485760, description="最大文件大小（字节）")
    UPLOAD_DIR: str = Field(default="./uploads", description="上传目录")
    
    # 监控配置
    ENABLE_METRICS: bool = Field(default=True, description="启用指标监控")
    METRICS_PORT: int = Field(default=9090, description="指标端口")
    
    @validator("ENVIRONMENT")
    def validate_environment(cls, v):
        """验证环境配置"""
        allowed_envs = ["development", "testing", "production"]
        if v not in allowed_envs:
            raise ValueError(f"Environment must be one of {allowed_envs}")
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        """验证日志级别"""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"Log level must be one of {allowed_levels}")
        return v.upper()
    
    @validator("LOG_FORMAT")
    def validate_log_format(cls, v):
        """验证日志格式"""
        allowed_formats = ["json", "text"]
        if v not in allowed_formats:
            raise ValueError(f"Log format must be one of {allowed_formats}")
        return v
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.ENVIRONMENT == "development"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.ENVIRONMENT == "production"
    
    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.ENVIRONMENT == "testing"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    return Settings()
