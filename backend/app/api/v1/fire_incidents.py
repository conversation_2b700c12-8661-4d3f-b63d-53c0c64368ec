"""
火情事件API路由

处理火情上报、查询和管理相关的API请求
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field
import structlog
from datetime import datetime
import uuid

from app.agents.state import FireIncident, FireLocation

logger = structlog.get_logger(__name__)

router = APIRouter()


class FireIncidentCreate(BaseModel):
    """创建火情事件请求模型"""
    location: FireLocation = Field(description="火情位置")
    severity: str = Field(description="严重程度")
    fire_type: str = Field(description="火灾类型")
    description: str = Field(description="火情描述")
    estimated_area: Optional[float] = Field(default=None, description="预估面积")
    wind_direction: Optional[str] = Field(default=None, description="风向")
    wind_speed: Optional[float] = Field(default=None, description="风速")
    weather_conditions: Optional[str] = Field(default=None, description="天气条件")


class FireIncidentResponse(BaseModel):
    """火情事件响应模型"""
    id: str = Field(description="事件ID")
    location: FireLocation = Field(description="火情位置")
    severity: str = Field(description="严重程度")
    fire_type: str = Field(description="火灾类型")
    description: str = Field(description="火情描述")
    reported_time: datetime = Field(description="上报时间")
    status: str = Field(description="事件状态")


# 模拟数据存储（生产环境应使用数据库）
fire_incidents_db: dict = {}


@router.post("/", response_model=FireIncidentResponse)
async def create_fire_incident(incident: FireIncidentCreate):
    """
    创建新的火情事件
    """
    try:
        # 生成事件ID
        incident_id = str(uuid.uuid4())
        
        # 创建火情事件
        fire_incident = FireIncident(
            id=incident_id,
            location=incident.location,
            severity=incident.severity,
            fire_type=incident.fire_type,
            description=incident.description,
            reported_time=datetime.now(),
            estimated_area=incident.estimated_area,
            wind_direction=incident.wind_direction,
            wind_speed=incident.wind_speed,
            weather_conditions=incident.weather_conditions
        )
        
        # 存储到模拟数据库
        fire_incidents_db[incident_id] = {
            "incident": fire_incident,
            "status": "reported",
            "created_time": datetime.now(),
            "updated_time": datetime.now()
        }
        
        logger.info(
            "Fire incident created",
            incident_id=incident_id,
            location=f"{incident.location.latitude},{incident.location.longitude}",
            severity=incident.severity
        )
        
        return FireIncidentResponse(
            id=incident_id,
            location=fire_incident.location,
            severity=fire_incident.severity,
            fire_type=fire_incident.fire_type,
            description=fire_incident.description,
            reported_time=fire_incident.reported_time,
            status="reported"
        )
        
    except Exception as e:
        logger.error("Failed to create fire incident", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create fire incident: {str(e)}"
        )


@router.get("/", response_model=List[FireIncidentResponse])
async def list_fire_incidents(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(10, ge=1, le=100, description="返回记录数"),
    severity: Optional[str] = Query(None, description="按严重程度筛选"),
    status: Optional[str] = Query(None, description="按状态筛选")
):
    """
    获取火情事件列表
    """
    try:
        incidents = []
        
        for incident_id, data in fire_incidents_db.items():
            incident = data["incident"]
            incident_status = data["status"]
            
            # 应用筛选条件
            if severity and incident.severity != severity:
                continue
            if status and incident_status != status:
                continue
            
            incidents.append(FireIncidentResponse(
                id=incident_id,
                location=incident.location,
                severity=incident.severity,
                fire_type=incident.fire_type,
                description=incident.description,
                reported_time=incident.reported_time,
                status=incident_status
            ))
        
        # 应用分页
        incidents = incidents[skip:skip + limit]
        
        logger.info(
            "Fire incidents listed",
            total_count=len(incidents),
            skip=skip,
            limit=limit
        )
        
        return incidents
        
    except Exception as e:
        logger.error("Failed to list fire incidents", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list fire incidents: {str(e)}"
        )


@router.get("/{incident_id}", response_model=FireIncidentResponse)
async def get_fire_incident(incident_id: str):
    """
    获取特定火情事件详情
    """
    if incident_id not in fire_incidents_db:
        raise HTTPException(
            status_code=404,
            detail="Fire incident not found"
        )
    
    try:
        data = fire_incidents_db[incident_id]
        incident = data["incident"]
        
        return FireIncidentResponse(
            id=incident_id,
            location=incident.location,
            severity=incident.severity,
            fire_type=incident.fire_type,
            description=incident.description,
            reported_time=incident.reported_time,
            status=data["status"]
        )
        
    except Exception as e:
        logger.error(
            "Failed to get fire incident",
            incident_id=incident_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get fire incident: {str(e)}"
        )


@router.put("/{incident_id}/status")
async def update_incident_status(incident_id: str, status: str):
    """
    更新火情事件状态
    """
    if incident_id not in fire_incidents_db:
        raise HTTPException(
            status_code=404,
            detail="Fire incident not found"
        )
    
    try:
        # 验证状态值
        valid_statuses = ["reported", "dispatched", "in_progress", "resolved", "cancelled"]
        if status not in valid_statuses:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid status. Must be one of: {valid_statuses}"
            )
        
        # 更新状态
        fire_incidents_db[incident_id]["status"] = status
        fire_incidents_db[incident_id]["updated_time"] = datetime.now()
        
        logger.info(
            "Fire incident status updated",
            incident_id=incident_id,
            new_status=status
        )
        
        return {
            "incident_id": incident_id,
            "status": status,
            "updated_time": datetime.now(),
            "message": "Status updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to update incident status",
            incident_id=incident_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update incident status: {str(e)}"
        )


@router.delete("/{incident_id}")
async def delete_fire_incident(incident_id: str):
    """
    删除火情事件
    """
    if incident_id not in fire_incidents_db:
        raise HTTPException(
            status_code=404,
            detail="Fire incident not found"
        )
    
    try:
        del fire_incidents_db[incident_id]
        
        logger.info("Fire incident deleted", incident_id=incident_id)
        
        return {
            "incident_id": incident_id,
            "message": "Fire incident deleted successfully"
        }
        
    except Exception as e:
        logger.error(
            "Failed to delete fire incident",
            incident_id=incident_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete fire incident: {str(e)}"
        )
