"""
调度API路由

处理智能调度相关的API请求，集成LangGraph Agent系统
"""

from typing import Dict, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
import structlog
import uuid
from datetime import datetime

from app.agents.graph import fire_dispatch_graph
from app.agents.state import FireIncident, FireLocation

logger = structlog.get_logger(__name__)

router = APIRouter()


class DispatchRequest(BaseModel):
    """调度请求模型"""
    fire_incident: FireIncident = Field(description="火情事件信息")
    user_id: str = Field(description="用户ID")
    priority: str = Field(default="medium", description="优先级")
    special_requirements: str = Field(default="", description="特殊要求")


class DispatchResponse(BaseModel):
    """调度响应模型"""
    session_id: str = Field(description="会话ID")
    status: str = Field(description="调度状态")
    message: str = Field(description="响应消息")
    estimated_completion_time: int = Field(description="预计完成时间（秒）")


class DispatchResult(BaseModel):
    """调度结果模型"""
    session_id: str = Field(description="会话ID")
    fire_incident_id: str = Field(description="火情事件ID")
    dispatch_decisions: list = Field(description="调度决策列表")
    coordination_tasks: list = Field(description="协调任务列表")
    total_response_time: int = Field(description="总响应时间（分钟）")
    status: str = Field(description="调度状态")
    created_time: datetime = Field(description="创建时间")


# 存储调度会话状态（生产环境应使用Redis或数据库）
dispatch_sessions: Dict[str, Dict[str, Any]] = {}


@router.post("/create", response_model=DispatchResponse)
async def create_dispatch(
    request: DispatchRequest,
    background_tasks: BackgroundTasks
):
    """
    创建新的调度任务
    
    启动LangGraph Agent系统进行智能调度分析
    """
    try:
        # 生成会话ID
        session_id = str(uuid.uuid4())
        
        logger.info(
            "Creating new dispatch session",
            session_id=session_id,
            user_id=request.user_id,
            fire_location=f"{request.fire_incident.location.latitude},{request.fire_incident.location.longitude}"
        )
        
        # 准备初始状态
        initial_state = {
            "session_id": session_id,
            "user_id": request.user_id,
            "fire_incident": request.fire_incident,
            "available_resources": [],
            "assigned_resources": [],
            "calculated_routes": [],
            "dispatch_decisions": [],
            "coordination_tasks": [],
            "messages": [],
            "situation_analysis": None,
            "resource_analysis": None,
            "route_analysis": None,
            "coordination_plan": None,
            "current_agent": None,
            "next_agent": None,
            "workflow_status": "started",
            "errors": [],
            "warnings": []
        }
        
        # 存储会话状态
        dispatch_sessions[session_id] = {
            "status": "processing",
            "created_time": datetime.now(),
            "initial_state": initial_state,
            "result": None
        }
        
        # 在后台执行Agent图
        background_tasks.add_task(
            execute_dispatch_workflow,
            session_id,
            initial_state
        )
        
        return DispatchResponse(
            session_id=session_id,
            status="processing",
            message="Dispatch workflow started successfully",
            estimated_completion_time=60  # 预计60秒完成
        )
        
    except Exception as e:
        logger.error("Failed to create dispatch", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create dispatch: {str(e)}"
        )


@router.get("/status/{session_id}")
async def get_dispatch_status(session_id: str):
    """
    获取调度任务状态
    """
    if session_id not in dispatch_sessions:
        raise HTTPException(
            status_code=404,
            detail="Dispatch session not found"
        )
    
    session = dispatch_sessions[session_id]
    
    return {
        "session_id": session_id,
        "status": session["status"],
        "created_time": session["created_time"],
        "has_result": session["result"] is not None
    }


@router.get("/result/{session_id}", response_model=DispatchResult)
async def get_dispatch_result(session_id: str):
    """
    获取调度结果
    """
    if session_id not in dispatch_sessions:
        raise HTTPException(
            status_code=404,
            detail="Dispatch session not found"
        )
    
    session = dispatch_sessions[session_id]
    
    if session["status"] != "completed":
        raise HTTPException(
            status_code=400,
            detail=f"Dispatch is still {session['status']}"
        )
    
    if not session["result"]:
        raise HTTPException(
            status_code=404,
            detail="Dispatch result not available"
        )
    
    result = session["result"]
    
    return DispatchResult(
        session_id=session_id,
        fire_incident_id=result.get("fire_incident", {}).get("id", "unknown"),
        dispatch_decisions=result.get("dispatch_decisions", []),
        coordination_tasks=result.get("coordination_tasks", []),
        total_response_time=_calculate_total_response_time(result),
        status=result.get("workflow_status", "unknown"),
        created_time=session["created_time"]
    )


@router.get("/stream/{session_id}")
async def stream_dispatch_progress(session_id: str):
    """
    流式获取调度进度（WebSocket的HTTP版本）
    """
    if session_id not in dispatch_sessions:
        raise HTTPException(
            status_code=404,
            detail="Dispatch session not found"
        )
    
    session = dispatch_sessions[session_id]
    
    # 返回当前进度信息
    progress = {
        "session_id": session_id,
        "status": session["status"],
        "messages": session.get("result", {}).get("messages", []) if session["result"] else [],
        "current_agent": session.get("result", {}).get("current_agent"),
        "workflow_status": session.get("result", {}).get("workflow_status", "started")
    }
    
    return progress


@router.delete("/{session_id}")
async def cancel_dispatch(session_id: str):
    """
    取消调度任务
    """
    if session_id not in dispatch_sessions:
        raise HTTPException(
            status_code=404,
            detail="Dispatch session not found"
        )
    
    # 标记为取消状态
    dispatch_sessions[session_id]["status"] = "cancelled"
    
    logger.info("Dispatch session cancelled", session_id=session_id)
    
    return {
        "session_id": session_id,
        "status": "cancelled",
        "message": "Dispatch session cancelled successfully"
    }


async def execute_dispatch_workflow(session_id: str, initial_state: dict):
    """
    执行调度工作流（后台任务）
    """
    try:
        logger.info("Starting dispatch workflow execution", session_id=session_id)
        
        # 调用LangGraph Agent图
        result = await fire_dispatch_graph.invoke(
            initial_state,
            config={"configurable": {"thread_id": session_id}}
        )
        
        # 更新会话状态
        dispatch_sessions[session_id].update({
            "status": "completed",
            "result": result,
            "completed_time": datetime.now()
        })
        
        logger.info(
            "Dispatch workflow completed successfully",
            session_id=session_id,
            workflow_status=result.get("workflow_status")
        )
        
    except Exception as e:
        logger.error(
            "Dispatch workflow execution failed",
            session_id=session_id,
            error=str(e),
            exc_info=True
        )
        
        # 更新为失败状态
        dispatch_sessions[session_id].update({
            "status": "failed",
            "error": str(e),
            "failed_time": datetime.now()
        })


def _calculate_total_response_time(result: dict) -> int:
    """计算总响应时间"""
    dispatch_decisions = result.get("dispatch_decisions", [])
    if dispatch_decisions:
        return dispatch_decisions[0].get("estimated_response_time", 0)
    return 0


@router.get("/health")
async def dispatch_health():
    """调度服务健康检查"""
    return {
        "status": "healthy",
        "service": "dispatch",
        "active_sessions": len(dispatch_sessions),
        "agent_graph_status": "ready"
    }
