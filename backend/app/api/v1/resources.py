"""
消防资源API路由

处理消防资源查询和管理相关的API请求
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field
import structlog
from datetime import datetime

from app.agents.state import ResourceInfo, FireLocation

logger = structlog.get_logger(__name__)

router = APIRouter()


class ResourceQuery(BaseModel):
    """资源查询请求模型"""
    latitude: float = Field(description="查询中心纬度")
    longitude: float = Field(description="查询中心经度")
    radius: float = Field(default=5000, description="查询半径（米）")
    resource_types: Optional[List[str]] = Field(default=None, description="资源类型筛选")
    status_filter: Optional[List[str]] = Field(default=None, description="状态筛选")


class ResourceResponse(BaseModel):
    """资源响应模型"""
    id: str = Field(description="资源ID")
    type: str = Field(description="资源类型")
    name: str = Field(description="资源名称")
    location: FireLocation = Field(description="资源位置")
    status: str = Field(description="资源状态")
    capacity: Optional[dict] = Field(default=None, description="资源能力")
    distance: Optional[float] = Field(default=None, description="距离查询点的距离")


# 模拟资源数据
mock_resources = [
    ResourceInfo(
        id="station_001",
        type="fire_station",
        name="Central Fire Station",
        location=FireLocation(
            latitude=39.9042,
            longitude=116.4074,
            address="123 Central Avenue, Beijing"
        ),
        status="available",
        capacity={
            "fire_trucks": 5,
            "personnel": 20,
            "water_capacity": 10000
        }
    ),
    ResourceInfo(
        id="station_002",
        type="fire_station",
        name="East Fire Station",
        location=FireLocation(
            latitude=39.9142,
            longitude=116.4174,
            address="456 East Road, Beijing"
        ),
        status="available",
        capacity={
            "fire_trucks": 3,
            "personnel": 15,
            "water_capacity": 8000
        }
    ),
    ResourceInfo(
        id="hydrant_001",
        type="fire_hydrant",
        name="Hydrant #001",
        location=FireLocation(
            latitude=39.9052,
            longitude=116.4084,
            address="Near Central Park"
        ),
        status="available",
        capacity={
            "water_pressure": "high",
            "flow_rate": 1000
        }
    ),
    ResourceInfo(
        id="hydrant_002",
        type="fire_hydrant",
        name="Hydrant #002",
        location=FireLocation(
            latitude=39.9032,
            longitude=116.4064,
            address="Main Street Corner"
        ),
        status="available",
        capacity={
            "water_pressure": "medium",
            "flow_rate": 800
        }
    ),
    ResourceInfo(
        id="truck_001",
        type="fire_truck",
        name="Fire Truck Alpha",
        location=FireLocation(
            latitude=39.9042,
            longitude=116.4074,
            address="Central Fire Station"
        ),
        status="available",
        capacity={
            "water_tank": 3000,
            "ladder_height": 30,
            "crew_size": 6
        }
    )
]


def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """
    计算两点间距离（简化版本，实际应使用更精确的地理计算）
    """
    import math
    
    # 将度转换为弧度
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    lat2_rad = math.radians(lat2)
    lon2_rad = math.radians(lon2)
    
    # 使用Haversine公式
    dlat = lat2_rad - lat1_rad
    dlon = lon2_rad - lon1_rad
    
    a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    
    # 地球半径（米）
    r = 6371000
    
    return c * r


@router.post("/search", response_model=List[ResourceResponse])
async def search_resources(query: ResourceQuery):
    """
    搜索指定位置周围的消防资源
    """
    try:
        logger.info(
            "Searching resources",
            center_lat=query.latitude,
            center_lon=query.longitude,
            radius=query.radius,
            resource_types=query.resource_types
        )
        
        results = []
        
        for resource in mock_resources:
            # 计算距离
            distance = calculate_distance(
                query.latitude, query.longitude,
                resource.location.latitude, resource.location.longitude
            )
            
            # 检查是否在搜索半径内
            if distance > query.radius:
                continue
            
            # 应用资源类型筛选
            if query.resource_types and resource.type not in query.resource_types:
                continue
            
            # 应用状态筛选
            if query.status_filter and resource.status not in query.status_filter:
                continue
            
            # 添加到结果
            results.append(ResourceResponse(
                id=resource.id,
                type=resource.type,
                name=resource.name,
                location=resource.location,
                status=resource.status,
                capacity=resource.capacity,
                distance=round(distance, 2)
            ))
        
        # 按距离排序
        results.sort(key=lambda x: x.distance or 0)
        
        logger.info(
            "Resource search completed",
            found_count=len(results),
            center_lat=query.latitude,
            center_lon=query.longitude
        )
        
        return results
        
    except Exception as e:
        logger.error("Failed to search resources", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to search resources: {str(e)}"
        )


@router.get("/", response_model=List[ResourceResponse])
async def list_resources(
    resource_type: Optional[str] = Query(None, description="资源类型筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(10, ge=1, le=100, description="返回记录数")
):
    """
    获取所有消防资源列表
    """
    try:
        results = []
        
        for resource in mock_resources:
            # 应用筛选条件
            if resource_type and resource.type != resource_type:
                continue
            if status and resource.status != status:
                continue
            
            results.append(ResourceResponse(
                id=resource.id,
                type=resource.type,
                name=resource.name,
                location=resource.location,
                status=resource.status,
                capacity=resource.capacity,
                distance=None
            ))
        
        # 应用分页
        results = results[skip:skip + limit]
        
        logger.info(
            "Resources listed",
            total_count=len(results),
            resource_type=resource_type,
            status=status
        )
        
        return results
        
    except Exception as e:
        logger.error("Failed to list resources", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list resources: {str(e)}"
        )


@router.get("/{resource_id}", response_model=ResourceResponse)
async def get_resource(resource_id: str):
    """
    获取特定资源详情
    """
    try:
        # 查找资源
        resource = None
        for r in mock_resources:
            if r.id == resource_id:
                resource = r
                break
        
        if not resource:
            raise HTTPException(
                status_code=404,
                detail="Resource not found"
            )
        
        return ResourceResponse(
            id=resource.id,
            type=resource.type,
            name=resource.name,
            location=resource.location,
            status=resource.status,
            capacity=resource.capacity,
            distance=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get resource",
            resource_id=resource_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get resource: {str(e)}"
        )


@router.put("/{resource_id}/status")
async def update_resource_status(resource_id: str, status: str):
    """
    更新资源状态
    """
    try:
        # 验证状态值
        valid_statuses = ["available", "busy", "maintenance", "offline"]
        if status not in valid_statuses:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid status. Must be one of: {valid_statuses}"
            )
        
        # 查找并更新资源
        resource_found = False
        for resource in mock_resources:
            if resource.id == resource_id:
                resource.status = status
                resource_found = True
                break
        
        if not resource_found:
            raise HTTPException(
                status_code=404,
                detail="Resource not found"
            )
        
        logger.info(
            "Resource status updated",
            resource_id=resource_id,
            new_status=status
        )
        
        return {
            "resource_id": resource_id,
            "status": status,
            "updated_time": datetime.now(),
            "message": "Resource status updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to update resource status",
            resource_id=resource_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update resource status: {str(e)}"
        )


@router.get("/types/list")
async def get_resource_types():
    """
    获取所有资源类型
    """
    return {
        "resource_types": [
            {"type": "fire_station", "name": "消防站", "description": "消防站点"},
            {"type": "fire_hydrant", "name": "消火栓", "description": "消防栓"},
            {"type": "fire_truck", "name": "消防车", "description": "消防车辆"},
            {"type": "rescue_team", "name": "救援队", "description": "救援团队"}
        ]
    }


@router.get("/statistics/summary")
async def get_resource_statistics():
    """
    获取资源统计信息
    """
    try:
        stats = {
            "total_resources": len(mock_resources),
            "by_type": {},
            "by_status": {},
            "last_updated": datetime.now()
        }
        
        # 按类型统计
        for resource in mock_resources:
            stats["by_type"][resource.type] = stats["by_type"].get(resource.type, 0) + 1
            stats["by_status"][resource.status] = stats["by_status"].get(resource.status, 0) + 1
        
        return stats
        
    except Exception as e:
        logger.error("Failed to get resource statistics", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get resource statistics: {str(e)}"
        )
