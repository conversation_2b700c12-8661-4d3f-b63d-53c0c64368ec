"""
消防资源API路由

处理消防资源查询和管理相关的API请求
基于真实数据库数据
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field
import structlog
from datetime import datetime

from app.models.fire_station import (
    FireStationSearchRequest, 
    FireStationSearchResponse,
    FireStationLocation
)
from app.services.fire_station_service import fire_station_service

logger = structlog.get_logger(__name__)

router = APIRouter()


class ResourceQuery(BaseModel):
    """资源查询请求模型"""
    latitude: float = Field(description="查询中心纬度")
    longitude: float = Field(description="查询中心经度")
    radius: float = Field(default=5000, description="查询半径（米）")
    station_types: Optional[List[str]] = Field(default=None, description="站点类型筛选")
    dadui_filter: Optional[str] = Field(default=None, description="大队筛选")
    status_filter: Optional[str] = Field(default=None, description="状态筛选")


class ResourceResponse(BaseModel):
    """资源响应模型"""
    id: str = Field(description="资源ID")
    type: str = Field(description="资源类型")
    name: str = Field(description="资源名称")
    address: Optional[str] = Field(default=None, description="地址")
    latitude: float = Field(description="纬度")
    longitude: float = Field(description="经度")
    dadui: Optional[str] = Field(default=None, description="所属大队")
    status: Optional[str] = Field(default=None, description="状态")
    distance: Optional[float] = Field(default=None, description="距离查询点的距离（米）")
    img_url: Optional[str] = Field(default=None, description="图片URL")


# 消防站类型映射
STATION_TYPE_MAP = {
    "fulltime": "专职消防站",
    "workstation": "消防工作站"
}


@router.post("/search", response_model=List[ResourceResponse])
async def search_resources(query: ResourceQuery):
    """
    搜索指定位置周围的消防资源（基于真实数据库数据）
    """
    try:
        logger.info(
            "Searching fire stations",
            center_lat=query.latitude,
            center_lon=query.longitude,
            radius=query.radius,
            station_types=query.station_types
        )
        
        # 构建搜索请求
        search_request = FireStationSearchRequest(
            latitude=query.latitude,
            longitude=query.longitude,
            radius=query.radius,
            station_types=query.station_types,
            dadui_filter=query.dadui_filter,
            status_filter=query.status_filter
        )
        
        # 调用服务层搜索
        search_response = await fire_station_service.search_nearby_stations(search_request)
        
        # 转换为API响应格式
        results = []
        for station in search_response.stations:
            # 计算距离
            distance = fire_station_service.calculate_distance(
                query.latitude, query.longitude,
                station.latitude, station.longitude
            )
            
            results.append(ResourceResponse(
                id=station.id,
                type=STATION_TYPE_MAP.get(station.station_type, station.station_type),
                name=station.name,
                address=station.address,
                latitude=station.latitude,
                longitude=station.longitude,
                dadui=station.dadui,
                status=station.status,
                distance=round(distance, 2),
                img_url=station.img_url
            ))
        
        logger.info(
            "Fire station search completed",
            found_count=len(results),
            center_lat=query.latitude,
            center_lon=query.longitude
        )
        
        return results
        
    except Exception as e:
        logger.error("Failed to search fire stations", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to search fire stations: {str(e)}"
        )


@router.get("/", response_model=List[ResourceResponse])
async def list_resources(
    station_type: Optional[str] = Query(None, description="站点类型筛选 (fulltime/workstation)"),
    dadui: Optional[str] = Query(None, description="大队筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(10, ge=1, le=100, description="返回记录数")
):
    """
    获取所有消防站列表（基于真实数据库数据）
    """
    try:
        results = []
        
        # 获取专职消防站
        if not station_type or station_type == "fulltime":
            fulltime_stations = await fire_station_service.get_fulltime_stations()
            for station in fulltime_stations:
                if station.get('horizontal') is not None and station.get('vertical') is not None:
                    # 应用筛选条件
                    if dadui and station.get('dadui') != dadui:
                        continue
                    if status and station.get('statue') != status:
                        continue
                    
                    results.append(ResourceResponse(
                        id=station['id'],
                        type=STATION_TYPE_MAP["fulltime"],
                        name=station.get('name') or "未知专职消防站",
                        address=station.get('address'),
                        latitude=station['vertical'],
                        longitude=station['horizontal'],
                        dadui=station.get('dadui'),
                        status=station.get('statue'),
                        distance=None,
                        img_url=station.get('img_url')
                    ))
        
        # 获取消防工作站
        if not station_type or station_type == "workstation":
            workstations = await fire_station_service.get_workstations()
            for station in workstations:
                if station.get('horizontal') is not None and station.get('vertical') is not None:
                    # 应用筛选条件
                    if dadui and station.get('dadui') != dadui:
                        continue
                    # 工作站默认为活跃状态
                    if status and status != "active":
                        continue
                    
                    results.append(ResourceResponse(
                        id=str(station['id']),
                        type=STATION_TYPE_MAP["workstation"],
                        name=station.get('name') or "未知消防工作站",
                        address=station.get('address'),
                        latitude=station['vertical'],
                        longitude=station['horizontal'],
                        dadui=station.get('dadui'),
                        status="active",
                        distance=None,
                        img_url=station.get('img_url')
                    ))
        
        # 应用分页
        total_count = len(results)
        results = results[skip:skip + limit]
        
        logger.info(
            "Fire stations listed",
            total_count=total_count,
            returned_count=len(results),
            station_type=station_type,
            dadui=dadui,
            status=status
        )
        
        return results
        
    except Exception as e:
        logger.error("Failed to list fire stations", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list fire stations: {str(e)}"
        )


@router.get("/dadui/list")
async def get_dadui_list():
    """
    获取所有大队列表
    """
    try:
        dadui_list = await fire_station_service.get_dadui_list()
        
        return {
            "dadui_list": dadui_list,
            "total_count": len(dadui_list)
        }
        
    except Exception as e:
        logger.error("Failed to get dadui list", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get dadui list: {str(e)}"
        )


@router.get("/types/list")
async def get_station_types():
    """
    获取所有消防站类型
    """
    return {
        "station_types": [
            {"type": "fulltime", "name": "专职消防站", "description": "专职消防站点"},
            {"type": "workstation", "name": "消防工作站", "description": "街道消防工作站"}
        ]
    }


@router.get("/statistics/summary")
async def get_fire_station_statistics():
    """
    获取消防站统计信息（基于真实数据库数据）
    """
    try:
        statistics = await fire_station_service.get_statistics()
        
        return {
            "total_stations": statistics["total_stations"],
            "fulltime_stations": statistics["fulltime_stations"],
            "workstations": statistics["workstations"],
            "last_updated": datetime.now()
        }
        
    except Exception as e:
        logger.error("Failed to get fire station statistics", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get fire station statistics: {str(e)}"
        )
