"""
Supervisor Agent - 中央调度器

负责分析火情状况，决定调用哪个专业Agent，协调整个调度流程
"""

from typing import Dict, Any
from datetime import datetime
import structlog
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage
from langgraph.types import Command

from app.agents.state import FireCommandState, AgentNextStep, AgentMessage
from app.config.settings import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()


class SupervisorAgent:
    """中央调度Supervisor Agent"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model="gpt-4",
            temperature=0.1,
            api_key=settings.OPENAI_API_KEY
        )
        self.agent_name = "supervisor"
    
    async def __call__(self, state: FireCommandState) -> Command[AgentNextStep]:
        """
        Supervisor Agent主逻辑
        
        分析当前状态，决定下一步调用哪个Agent
        """
        try:
            logger.info("Supervisor agent processing", session_id=state.get("session_id"))
            
            # 分析当前状态
            analysis = await self._analyze_current_state(state)
            
            # 决定下一个Agent
            next_agent = await self._decide_next_agent(state, analysis)
            
            # 创建Agent消息
            message = AgentMessage(
                agent_name=self.agent_name,
                message_type="decision",
                content=f"Decided to call {next_agent} based on current state analysis",
                timestamp=datetime.now(),
                metadata={"analysis": analysis, "next_agent": next_agent}
            )
            
            # 更新状态
            updates = {
                "current_agent": self.agent_name,
                "next_agent": next_agent,
                "messages": [message]
            }
            
            logger.info(
                "Supervisor decision made",
                session_id=state.get("session_id"),
                next_agent=next_agent
            )
            
            return Command(
                goto=next_agent,
                update=updates
            )
            
        except Exception as e:
            logger.error(
                "Supervisor agent error",
                session_id=state.get("session_id"),
                error=str(e),
                exc_info=True
            )
            
            error_message = AgentMessage(
                agent_name=self.agent_name,
                message_type="error",
                content=f"Supervisor agent encountered an error: {str(e)}",
                timestamp=datetime.now(),
                metadata={"error": str(e)}
            )
            
            return Command(
                goto="__end__",
                update={
                    "workflow_status": "failed",
                    "errors": [f"Supervisor error: {str(e)}"],
                    "messages": [error_message]
                }
            )
    
    async def _analyze_current_state(self, state: FireCommandState) -> Dict[str, Any]:
        """分析当前状态"""
        
        fire_incident = state.get("fire_incident")
        available_resources = state.get("available_resources", [])
        assigned_resources = state.get("assigned_resources", [])
        calculated_routes = state.get("calculated_routes", [])
        dispatch_decisions = state.get("dispatch_decisions", [])
        workflow_status = state.get("workflow_status", "started")
        
        analysis = {
            "has_fire_incident": fire_incident is not None,
            "fire_severity": fire_incident.severity if fire_incident else None,
            "has_situation_analysis": state.get("situation_analysis") is not None,
            "has_available_resources": len(available_resources) > 0,
            "has_resource_analysis": state.get("resource_analysis") is not None,
            "has_assigned_resources": len(assigned_resources) > 0,
            "has_calculated_routes": len(calculated_routes) > 0,
            "has_route_analysis": state.get("route_analysis") is not None,
            "has_dispatch_decisions": len(dispatch_decisions) > 0,
            "has_coordination_plan": state.get("coordination_plan") is not None,
            "workflow_status": workflow_status,
            "total_messages": len(state.get("messages", [])),
            "has_errors": len(state.get("errors", [])) > 0
        }
        
        return analysis
    
    async def _decide_next_agent(self, state: FireCommandState, analysis: Dict[str, Any]) -> AgentNextStep:
        """决定下一个要调用的Agent"""
        
        # 如果有错误，结束流程
        if analysis["has_errors"]:
            return "__end__"
        
        # 如果没有火情信息，无法继续
        if not analysis["has_fire_incident"]:
            return "__end__"
        
        # 构建决策提示
        system_prompt = """
        你是消防指挥调度系统的中央调度器。根据当前状态分析，决定下一步应该调用哪个专业Agent。
        
        可用的Agent：
        1. situation_agent - 态势感知Agent，分析火情和环境
        2. resource_agent - 资源调度Agent，查找和分配消防资源
        3. route_agent - 路径规划Agent，计算最优救援路径
        4. coordination_agent - 协调指挥Agent，制定协调计划
        5. __end__ - 结束流程
        
        决策原则：
        - 首先进行态势感知分析
        - 然后进行资源调度
        - 接着进行路径规划
        - 最后进行协调指挥
        - 如果所有步骤都完成，则结束流程
        
        请根据当前状态分析，返回下一个应该调用的Agent名称。
        """
        
        user_prompt = f"""
        当前状态分析：
        {analysis}
        
        火情严重程度：{analysis.get('fire_severity', 'unknown')}
        
        请决定下一步应该调用哪个Agent。只返回Agent名称，不要其他内容。
        """
        
        try:
            response = await self.llm.ainvoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ])
            
            next_agent = response.content.strip().lower()
            
            # 验证返回的Agent名称
            valid_agents = [
                "situation_agent", "resource_agent", "route_agent", 
                "coordination_agent", "__end__"
            ]
            
            if next_agent not in valid_agents:
                # 如果LLM返回无效的Agent名称，使用默认逻辑
                next_agent = self._default_decision_logic(analysis)
            
            return next_agent
            
        except Exception as e:
            logger.error("Error in LLM decision making", error=str(e))
            # 如果LLM调用失败，使用默认逻辑
            return self._default_decision_logic(analysis)
    
    def _default_decision_logic(self, analysis: Dict[str, Any]) -> AgentNextStep:
        """默认决策逻辑（当LLM调用失败时使用）"""
        
        # 按照标准流程进行决策
        if not analysis["has_situation_analysis"]:
            return "situation_agent"
        elif not analysis["has_resource_analysis"]:
            return "resource_agent"
        elif not analysis["has_route_analysis"]:
            return "route_agent"
        elif not analysis["has_coordination_plan"]:
            return "coordination_agent"
        else:
            return "__end__"


# 创建全局实例
supervisor_agent = SupervisorAgent()
