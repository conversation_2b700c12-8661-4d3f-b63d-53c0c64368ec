"""
LangGraph Agent图构建

构建消防指挥调度系统的Agent协作图
"""

import structlog
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import InMemorySaver

from app.agents.state import FireCommandState
from app.agents.supervisor import supervisor_agent
# from app.agents.situation import situation_agent
# from app.agents.resource import resource_agent
# from app.agents.route import route_agent
# from app.agents.coordination import coordination_agent

logger = structlog.get_logger(__name__)


class FireDispatchGraph:
    """消防调度Agent图"""
    
    def __init__(self):
        self.graph = None
        self.checkpointer = InMemorySaver()
        self._build_graph()
    
    def _build_graph(self):
        """构建Agent协作图"""
        
        logger.info("Building fire dispatch agent graph")
        
        # 创建StateGraph
        builder = StateGraph(FireCommandState)
        
        # 添加Supervisor Agent
        builder.add_node("supervisor", supervisor_agent)
        
        # 添加专业Agent（暂时使用占位符）
        builder.add_node("situation_agent", self._situation_agent_placeholder)
        builder.add_node("resource_agent", self._resource_agent_placeholder)
        builder.add_node("route_agent", self._route_agent_placeholder)
        builder.add_node("coordination_agent", self._coordination_agent_placeholder)
        
        # 设置入口点
        builder.add_edge(START, "supervisor")
        
        # 所有专业Agent完成后都返回到Supervisor
        builder.add_edge("situation_agent", "supervisor")
        builder.add_edge("resource_agent", "supervisor")
        builder.add_edge("route_agent", "supervisor")
        builder.add_edge("coordination_agent", "supervisor")
        
        # 编译图
        self.graph = builder.compile(checkpointer=self.checkpointer)
        
        logger.info("Fire dispatch agent graph built successfully")
    
    async def _situation_agent_placeholder(self, state: FireCommandState):
        """态势感知Agent占位符"""
        from datetime import datetime
        from app.agents.state import AgentMessage
        
        logger.info("Situation agent placeholder called", session_id=state.get("session_id"))
        
        # 模拟态势分析
        situation_analysis = {
            "fire_risk_level": "high",
            "environmental_factors": {
                "wind_speed": 5.2,
                "wind_direction": "northeast",
                "temperature": 28,
                "humidity": 45
            },
            "threat_assessment": {
                "spread_risk": "medium",
                "structural_risk": "high",
                "evacuation_needed": True
            },
            "analysis_timestamp": datetime.now().isoformat()
        }
        
        message = AgentMessage(
            agent_name="situation_agent",
            message_type="analysis",
            content="Completed situation analysis and risk assessment",
            timestamp=datetime.now(),
            metadata={"analysis": situation_analysis}
        )
        
        return {
            "situation_analysis": situation_analysis,
            "messages": [message],
            "current_agent": "situation_agent"
        }
    
    async def _resource_agent_placeholder(self, state: FireCommandState):
        """资源调度Agent占位符"""
        from datetime import datetime
        from app.agents.state import AgentMessage, ResourceInfo, FireLocation
        
        logger.info("Resource agent placeholder called", session_id=state.get("session_id"))
        
        # 模拟资源分析和分配
        fire_incident = state.get("fire_incident")
        if not fire_incident:
            return {"errors": ["No fire incident information available"]}
        
        # 模拟找到的资源
        mock_resources = [
            ResourceInfo(
                id="station_001",
                type="fire_station",
                name="Central Fire Station",
                location=FireLocation(
                    latitude=fire_incident.location.latitude + 0.01,
                    longitude=fire_incident.location.longitude + 0.01,
                    address="123 Fire Station Road"
                ),
                status="available",
                distance_to_fire=1200,
                estimated_arrival_time=8
            ),
            ResourceInfo(
                id="hydrant_001",
                type="fire_hydrant",
                name="Hydrant #001",
                location=FireLocation(
                    latitude=fire_incident.location.latitude + 0.005,
                    longitude=fire_incident.location.longitude + 0.005,
                    address="Near fire location"
                ),
                status="available",
                distance_to_fire=500,
                estimated_arrival_time=0
            )
        ]
        
        resource_analysis = {
            "total_resources_found": len(mock_resources),
            "nearest_station_distance": 1200,
            "estimated_response_time": 8,
            "resource_adequacy": "sufficient",
            "analysis_timestamp": datetime.now().isoformat()
        }
        
        message = AgentMessage(
            agent_name="resource_agent",
            message_type="analysis",
            content=f"Found {len(mock_resources)} available resources",
            timestamp=datetime.now(),
            metadata={"analysis": resource_analysis}
        )
        
        return {
            "available_resources": mock_resources,
            "resource_analysis": resource_analysis,
            "messages": [message],
            "current_agent": "resource_agent"
        }
    
    async def _route_agent_placeholder(self, state: FireCommandState):
        """路径规划Agent占位符"""
        from datetime import datetime
        from app.agents.state import AgentMessage, RouteInfo
        
        logger.info("Route agent placeholder called", session_id=state.get("session_id"))
        
        fire_incident = state.get("fire_incident")
        available_resources = state.get("available_resources", [])
        
        if not fire_incident or not available_resources:
            return {"errors": ["Missing fire incident or resource information"]}
        
        # 模拟路径规划
        mock_routes = []
        for resource in available_resources:
            if resource.type == "fire_station":
                route = RouteInfo(
                    start_location=resource.location,
                    end_location=fire_incident.location,
                    distance=resource.distance_to_fire or 1000,
                    duration=resource.estimated_arrival_time or 10,
                    route_points=[
                        [resource.location.longitude, resource.location.latitude],
                        [fire_incident.location.longitude, fire_incident.location.latitude]
                    ],
                    traffic_condition="moderate"
                )
                mock_routes.append(route)
        
        route_analysis = {
            "total_routes_calculated": len(mock_routes),
            "fastest_route_time": min([r.duration for r in mock_routes]) if mock_routes else 0,
            "average_distance": sum([r.distance for r in mock_routes]) / len(mock_routes) if mock_routes else 0,
            "traffic_impact": "minimal",
            "analysis_timestamp": datetime.now().isoformat()
        }
        
        message = AgentMessage(
            agent_name="route_agent",
            message_type="analysis",
            content=f"Calculated {len(mock_routes)} optimal routes",
            timestamp=datetime.now(),
            metadata={"analysis": route_analysis}
        )
        
        return {
            "calculated_routes": mock_routes,
            "route_analysis": route_analysis,
            "messages": [message],
            "current_agent": "route_agent"
        }
    
    async def _coordination_agent_placeholder(self, state: FireCommandState):
        """协调指挥Agent占位符"""
        from datetime import datetime
        from app.agents.state import AgentMessage, DispatchDecision, CoordinationTask
        
        logger.info("Coordination agent placeholder called", session_id=state.get("session_id"))
        
        fire_incident = state.get("fire_incident")
        available_resources = state.get("available_resources", [])
        calculated_routes = state.get("calculated_routes", [])
        
        if not all([fire_incident, available_resources, calculated_routes]):
            return {"errors": ["Missing required information for coordination"]}
        
        # 模拟调度决策
        dispatch_decision = DispatchDecision(
            decision_id=f"dispatch_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            fire_incident_id=fire_incident.id or "unknown",
            assigned_resources=available_resources[:2],  # 分配前两个资源
            primary_route=calculated_routes[0] if calculated_routes else None,
            backup_routes=calculated_routes[1:],
            priority="high",
            estimated_response_time=8,
            special_instructions="Ensure water supply from nearest hydrant",
            created_time=datetime.now()
        )
        
        # 模拟协调任务
        coordination_tasks = [
            CoordinationTask(
                task_id="task_001",
                task_type="resource_deployment",
                assigned_department="Fire Department",
                description="Deploy fire trucks to incident location",
                status="pending",
                priority="high",
                deadline=datetime.now()
            ),
            CoordinationTask(
                task_id="task_002",
                task_type="traffic_control",
                assigned_department="Traffic Police",
                description="Clear emergency route and manage traffic",
                status="pending",
                priority="medium",
                deadline=datetime.now()
            )
        ]
        
        coordination_plan = {
            "total_tasks": len(coordination_tasks),
            "departments_involved": ["Fire Department", "Traffic Police"],
            "estimated_coordination_time": 15,
            "plan_status": "ready",
            "analysis_timestamp": datetime.now().isoformat()
        }
        
        message = AgentMessage(
            agent_name="coordination_agent",
            message_type="plan",
            content="Created dispatch decision and coordination plan",
            timestamp=datetime.now(),
            metadata={"plan": coordination_plan}
        )
        
        return {
            "dispatch_decisions": [dispatch_decision],
            "coordination_tasks": coordination_tasks,
            "coordination_plan": coordination_plan,
            "messages": [message],
            "current_agent": "coordination_agent",
            "workflow_status": "completed"
        }
    
    async def invoke(self, initial_state: dict, config: dict = None):
        """调用Agent图"""
        if config is None:
            config = {"configurable": {"thread_id": initial_state.get("session_id", "default")}}
        
        logger.info("Invoking fire dispatch agent graph", session_id=initial_state.get("session_id"))
        
        try:
            result = await self.graph.ainvoke(initial_state, config)
            logger.info("Agent graph execution completed", session_id=initial_state.get("session_id"))
            return result
        except Exception as e:
            logger.error("Agent graph execution failed", error=str(e), exc_info=True)
            raise
    
    async def stream(self, initial_state: dict, config: dict = None):
        """流式调用Agent图"""
        if config is None:
            config = {"configurable": {"thread_id": initial_state.get("session_id", "default")}}
        
        logger.info("Streaming fire dispatch agent graph", session_id=initial_state.get("session_id"))
        
        try:
            async for chunk in self.graph.astream(initial_state, config):
                yield chunk
        except Exception as e:
            logger.error("Agent graph streaming failed", error=str(e), exc_info=True)
            raise


# 创建全局图实例
fire_dispatch_graph = FireDispatchGraph()
