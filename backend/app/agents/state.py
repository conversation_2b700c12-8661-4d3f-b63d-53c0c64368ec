"""
LangGraph Agent状态定义

定义消防指挥调度系统中各Agent的状态结构
"""

from typing import List, Optional, Dict, Any, Literal
from typing_extensions import TypedDict, Annotated
from pydantic import BaseModel, Field
from datetime import datetime
import operator


class FireLocation(BaseModel):
    """火情位置信息"""
    latitude: float = Field(description="纬度")
    longitude: float = Field(description="经度")
    address: str = Field(description="详细地址")
    district: Optional[str] = Field(default=None, description="所属辖区")


class FireIncident(BaseModel):
    """火情事件信息"""
    id: Optional[str] = Field(default=None, description="事件ID")
    location: FireLocation = Field(description="火情位置")
    severity: Literal["low", "medium", "high", "critical"] = Field(description="严重程度")
    fire_type: str = Field(description="火灾类型")
    description: str = Field(description="火情描述")
    reported_time: datetime = Field(description="上报时间")
    estimated_area: Optional[float] = Field(default=None, description="预估面积（平方米）")
    wind_direction: Optional[str] = Field(default=None, description="风向")
    wind_speed: Optional[float] = Field(default=None, description="风速（m/s）")
    weather_conditions: Optional[str] = Field(default=None, description="天气条件")


class ResourceInfo(BaseModel):
    """消防资源信息"""
    id: str = Field(description="资源ID")
    type: Literal["fire_hydrant", "fire_station", "fire_truck", "rescue_team"] = Field(description="资源类型")
    name: str = Field(description="资源名称")
    location: FireLocation = Field(description="资源位置")
    status: Literal["available", "busy", "maintenance", "offline"] = Field(description="资源状态")
    capacity: Optional[Dict[str, Any]] = Field(default=None, description="资源能力")
    distance_to_fire: Optional[float] = Field(default=None, description="到火场距离（米）")
    estimated_arrival_time: Optional[int] = Field(default=None, description="预计到达时间（分钟）")


class RouteInfo(BaseModel):
    """路径信息"""
    start_location: FireLocation = Field(description="起始位置")
    end_location: FireLocation = Field(description="目标位置")
    distance: float = Field(description="距离（米）")
    duration: int = Field(description="预计时间（分钟）")
    route_points: List[List[float]] = Field(description="路径点坐标列表")
    traffic_condition: Optional[str] = Field(default=None, description="交通状况")
    road_restrictions: Optional[List[str]] = Field(default=None, description="道路限制")


class DispatchDecision(BaseModel):
    """调度决策"""
    decision_id: str = Field(description="决策ID")
    fire_incident_id: str = Field(description="火情事件ID")
    assigned_resources: List[ResourceInfo] = Field(description="分配的资源")
    primary_route: RouteInfo = Field(description="主要路径")
    backup_routes: List[RouteInfo] = Field(default=[], description="备用路径")
    priority: Literal["low", "medium", "high", "urgent"] = Field(description="优先级")
    estimated_response_time: int = Field(description="预计响应时间（分钟）")
    special_instructions: Optional[str] = Field(default=None, description="特殊指令")
    created_time: datetime = Field(description="创建时间")


class CoordinationTask(BaseModel):
    """协调任务"""
    task_id: str = Field(description="任务ID")
    task_type: str = Field(description="任务类型")
    assigned_department: str = Field(description="负责部门")
    description: str = Field(description="任务描述")
    status: Literal["pending", "in_progress", "completed", "cancelled"] = Field(description="任务状态")
    priority: Literal["low", "medium", "high", "urgent"] = Field(description="优先级")
    deadline: Optional[datetime] = Field(default=None, description="截止时间")
    dependencies: List[str] = Field(default=[], description="依赖任务ID列表")


class AgentMessage(BaseModel):
    """Agent消息"""
    agent_name: str = Field(description="Agent名称")
    message_type: str = Field(description="消息类型")
    content: str = Field(description="消息内容")
    timestamp: datetime = Field(description="时间戳")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="元数据")


# LangGraph状态定义
class FireCommandState(TypedDict):
    """消防指挥调度系统主状态"""
    
    # 基础信息
    session_id: str
    user_id: Optional[str]
    
    # 火情信息
    fire_incident: Optional[FireIncident]
    
    # 资源信息
    available_resources: Annotated[List[ResourceInfo], operator.add]
    assigned_resources: Annotated[List[ResourceInfo], operator.add]
    
    # 路径信息
    calculated_routes: Annotated[List[RouteInfo], operator.add]
    
    # 调度决策
    dispatch_decisions: Annotated[List[DispatchDecision], operator.add]
    
    # 协调任务
    coordination_tasks: Annotated[List[CoordinationTask], operator.add]
    
    # Agent通信
    messages: Annotated[List[AgentMessage], operator.add]
    
    # 分析结果
    situation_analysis: Optional[Dict[str, Any]]
    resource_analysis: Optional[Dict[str, Any]]
    route_analysis: Optional[Dict[str, Any]]
    coordination_plan: Optional[Dict[str, Any]]
    
    # 控制信息
    current_agent: Optional[str]
    next_agent: Optional[str]
    workflow_status: Literal["started", "in_progress", "completed", "failed"]
    
    # 错误信息
    errors: Annotated[List[str], operator.add]
    warnings: Annotated[List[str], operator.add]


class SituationAgentState(TypedDict):
    """态势感知Agent状态"""
    fire_incident: FireIncident
    environmental_data: Optional[Dict[str, Any]]
    risk_assessment: Optional[Dict[str, Any]]
    threat_analysis: Optional[Dict[str, Any]]


class ResourceAgentState(TypedDict):
    """资源调度Agent状态"""
    fire_incident: FireIncident
    available_resources: List[ResourceInfo]
    resource_requirements: Optional[Dict[str, Any]]
    allocation_strategy: Optional[Dict[str, Any]]


class RouteAgentState(TypedDict):
    """路径规划Agent状态"""
    fire_incident: FireIncident
    assigned_resources: List[ResourceInfo]
    traffic_data: Optional[Dict[str, Any]]
    route_options: List[RouteInfo]


class CoordinationAgentState(TypedDict):
    """协调指挥Agent状态"""
    fire_incident: FireIncident
    dispatch_decisions: List[DispatchDecision]
    coordination_tasks: List[CoordinationTask]
    department_status: Optional[Dict[str, Any]]


# Agent返回类型定义
AgentNextStep = Literal[
    "supervisor",
    "situation_agent", 
    "resource_agent",
    "route_agent", 
    "coordination_agent",
    "__end__"
]
