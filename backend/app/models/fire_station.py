"""
消防站数据模型

基于现有数据库结构定义的消防站相关模型
"""

from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime


class FireStationLocation(BaseModel):
    """消防站位置信息（统一格式）"""
    
    id: str = Field(description="站点ID")
    name: str = Field(description="站点名称")
    address: Optional[str] = Field(default=None, description="地址")
    latitude: float = Field(description="纬度")
    longitude: float = Field(description="经度")
    station_type: str = Field(description="站点类型", examples=["fulltime", "workstation"])
    dadui: Optional[str] = Field(default=None, description="所属大队")
    status: Optional[str] = Field(default=None, description="状态")
    img_url: Optional[str] = Field(default=None, description="图片URL")


class FireStationSearchRequest(BaseModel):
    """消防站搜索请求"""
    
    latitude: float = Field(description="搜索中心纬度")
    longitude: float = Field(description="搜索中心经度")
    radius: float = Field(default=5000, description="搜索半径（米）")
    station_types: Optional[list[str]] = Field(default=None, description="站点类型筛选")
    dadui_filter: Optional[str] = Field(default=None, description="大队筛选")
    status_filter: Optional[str] = Field(default=None, description="状态筛选")


class FireStationSearchResponse(BaseModel):
    """消防站搜索响应"""
    
    total_count: int = Field(description="总数量")
    stations: list[FireStationLocation] = Field(description="站点列表")
    search_center: dict = Field(description="搜索中心")
    search_radius: float = Field(description="搜索半径")
