"""
数据库连接管理

使用asyncpg进行PostgreSQL异步连接
"""

import asyncpg
import structlog
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager

from app.config.settings import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self.schema = settings.DATABASE_SCHEMA
        
        # 解析数据库URL
        self._parse_database_url()
    
    def _parse_database_url(self):
        """解析数据库连接URL"""
        # postgresql://username:password@host:port/database
        url = settings.DATABASE_URL
        
        if url.startswith('postgresql://'):
            url = url[13:]  # 移除 postgresql://
        
        # 分离用户信息和主机信息
        if '@' in url:
            user_info, host_info = url.split('@', 1)
            if ':' in user_info:
                self.username, self.password = user_info.split(':', 1)
            else:
                self.username = user_info
                self.password = None
        else:
            raise ValueError("Invalid database URL format")
        
        # 分离主机和数据库
        if '/' in host_info:
            host_port, self.database = host_info.split('/', 1)
        else:
            raise ValueError("Database name not specified in URL")
        
        # 分离主机和端口
        if ':' in host_port:
            self.host, port_str = host_port.split(':', 1)
            self.port = int(port_str)
        else:
            self.host = host_port
            self.port = 5432
    
    async def create_pool(self):
        """创建连接池"""
        try:
            self.pool = await asyncpg.create_pool(
                user=self.username,
                password=self.password,
                host=self.host,
                port=self.port,
                database=self.database,
                min_size=5,
                max_size=settings.DATABASE_POOL_SIZE,
                command_timeout=60
            )
            
            logger.info(
                "Database pool created successfully",
                host=self.host,
                port=self.port,
                database=self.database,
                schema=self.schema
            )
            
        except Exception as e:
            logger.error(
                "Failed to create database pool",
                error=str(e),
                host=self.host,
                port=self.port,
                database=self.database
            )
            raise
    
    async def close_pool(self):
        """关闭连接池"""
        if self.pool:
            await self.pool.close()
            logger.info("Database pool closed")
    
    @asynccontextmanager
    async def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        if not self.pool:
            await self.create_pool()
        
        async with self.pool.acquire() as connection:
            try:
                yield connection
            except Exception as e:
                logger.error("Database operation failed", error=str(e))
                raise
    
    async def execute_query(self, query: str, *args) -> List[Dict[str, Any]]:
        """执行查询并返回结果"""
        async with self.get_connection() as conn:
            try:
                rows = await conn.fetch(query, *args)
                return [dict(row) for row in rows]
            except Exception as e:
                logger.error(
                    "Query execution failed",
                    query=query[:100] + "..." if len(query) > 100 else query,
                    error=str(e)
                )
                raise
    
    async def execute_query_one(self, query: str, *args) -> Optional[Dict[str, Any]]:
        """执行查询并返回单个结果"""
        async with self.get_connection() as conn:
            try:
                row = await conn.fetchrow(query, *args)
                return dict(row) if row else None
            except Exception as e:
                logger.error(
                    "Query execution failed",
                    query=query[:100] + "..." if len(query) > 100 else query,
                    error=str(e)
                )
                raise
    
    def get_table_name(self, table: str) -> str:
        """获取带模式的表名"""
        return f"{self.schema}.{table}"
    
    async def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            async with self.get_connection() as conn:
                result = await conn.fetchval("SELECT 1")
                if result == 1:
                    logger.info("Database connection test successful")
                    return True
                else:
                    logger.error("Database connection test failed")
                    return False
        except Exception as e:
            logger.error("Database connection test failed", error=str(e))
            return False


# 创建全局数据库管理器实例
db_manager = DatabaseManager()


async def init_database():
    """初始化数据库连接"""
    await db_manager.create_pool()
    
    # 测试连接
    if await db_manager.test_connection():
        logger.info("Database initialization successful")
    else:
        raise Exception("Database connection test failed")


async def close_database():
    """关闭数据库连接"""
    await db_manager.close_pool()


# 便捷函数
async def execute_query(query: str, *args) -> List[Dict[str, Any]]:
    """执行查询"""
    return await db_manager.execute_query(query, *args)


async def execute_query_one(query: str, *args) -> Optional[Dict[str, Any]]:
    """执行查询并返回单个结果"""
    return await db_manager.execute_query_one(query, *args)
