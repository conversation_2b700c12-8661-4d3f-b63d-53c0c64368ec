"""
WebSocket连接管理器

管理WebSocket连接，处理实时通信
"""

from typing import Dict, List, Set
import json
import asyncio
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
import structlog

logger = structlog.get_logger(__name__)


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃连接
        self.active_connections: Dict[str, WebSocket] = {}
        # 用户ID到连接ID的映射
        self.user_connections: Dict[str, Set[str]] = {}
        # 会话ID到连接ID的映射
        self.session_connections: Dict[str, Set[str]] = {}
        # 连接元数据
        self.connection_metadata: Dict[str, Dict] = {}
    
    async def connect(self, websocket: WebSocket, connection_id: str, user_id: str = None, session_id: str = None):
        """接受新的WebSocket连接"""
        await websocket.accept()
        
        # 存储连接
        self.active_connections[connection_id] = websocket
        
        # 存储元数据
        self.connection_metadata[connection_id] = {
            "user_id": user_id,
            "session_id": session_id,
            "connected_at": datetime.now(),
            "last_ping": datetime.now()
        }
        
        # 建立用户映射
        if user_id:
            if user_id not in self.user_connections:
                self.user_connections[user_id] = set()
            self.user_connections[user_id].add(connection_id)
        
        # 建立会话映射
        if session_id:
            if session_id not in self.session_connections:
                self.session_connections[session_id] = set()
            self.session_connections[session_id].add(connection_id)
        
        logger.info(
            "WebSocket connection established",
            connection_id=connection_id,
            user_id=user_id,
            session_id=session_id,
            total_connections=len(self.active_connections)
        )
    
    async def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        if connection_id not in self.active_connections:
            return
        
        # 获取连接元数据
        metadata = self.connection_metadata.get(connection_id, {})
        user_id = metadata.get("user_id")
        session_id = metadata.get("session_id")
        
        # 移除连接
        del self.active_connections[connection_id]
        del self.connection_metadata[connection_id]
        
        # 清理用户映射
        if user_id and user_id in self.user_connections:
            self.user_connections[user_id].discard(connection_id)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        # 清理会话映射
        if session_id and session_id in self.session_connections:
            self.session_connections[session_id].discard(connection_id)
            if not self.session_connections[session_id]:
                del self.session_connections[session_id]
        
        logger.info(
            "WebSocket connection closed",
            connection_id=connection_id,
            user_id=user_id,
            session_id=session_id,
            total_connections=len(self.active_connections)
        )
    
    async def send_personal_message(self, message: dict, connection_id: str):
        """发送个人消息"""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            try:
                await websocket.send_text(json.dumps(message))
                logger.debug("Personal message sent", connection_id=connection_id)
            except Exception as e:
                logger.error(
                    "Failed to send personal message",
                    connection_id=connection_id,
                    error=str(e)
                )
                await self.disconnect(connection_id)
    
    async def send_to_user(self, message: dict, user_id: str):
        """发送消息给特定用户的所有连接"""
        if user_id in self.user_connections:
            connection_ids = list(self.user_connections[user_id])
            for connection_id in connection_ids:
                await self.send_personal_message(message, connection_id)
            
            logger.debug(
                "Message sent to user",
                user_id=user_id,
                connections_count=len(connection_ids)
            )
    
    async def send_to_session(self, message: dict, session_id: str):
        """发送消息给特定会话的所有连接"""
        if session_id in self.session_connections:
            connection_ids = list(self.session_connections[session_id])
            for connection_id in connection_ids:
                await self.send_personal_message(message, connection_id)
            
            logger.debug(
                "Message sent to session",
                session_id=session_id,
                connections_count=len(connection_ids)
            )
    
    async def broadcast(self, message: dict):
        """广播消息给所有连接"""
        if not self.active_connections:
            return
        
        connection_ids = list(self.active_connections.keys())
        for connection_id in connection_ids:
            await self.send_personal_message(message, connection_id)
        
        logger.debug(
            "Message broadcasted",
            connections_count=len(connection_ids)
        )
    
    async def disconnect_all(self):
        """断开所有连接"""
        connection_ids = list(self.active_connections.keys())
        for connection_id in connection_ids:
            await self.disconnect(connection_id)
        
        logger.info("All WebSocket connections closed")
    
    async def ping_connection(self, connection_id: str):
        """发送ping消息"""
        ping_message = {
            "type": "ping",
            "timestamp": datetime.now().isoformat()
        }
        await self.send_personal_message(ping_message, connection_id)
    
    async def handle_pong(self, connection_id: str):
        """处理pong响应"""
        if connection_id in self.connection_metadata:
            self.connection_metadata[connection_id]["last_ping"] = datetime.now()
    
    def get_connection_info(self, connection_id: str) -> dict:
        """获取连接信息"""
        if connection_id not in self.connection_metadata:
            return None
        
        metadata = self.connection_metadata[connection_id]
        return {
            "connection_id": connection_id,
            "user_id": metadata.get("user_id"),
            "session_id": metadata.get("session_id"),
            "connected_at": metadata.get("connected_at"),
            "last_ping": metadata.get("last_ping"),
            "is_active": connection_id in self.active_connections
        }
    
    def get_statistics(self) -> dict:
        """获取连接统计信息"""
        return {
            "total_connections": len(self.active_connections),
            "total_users": len(self.user_connections),
            "total_sessions": len(self.session_connections),
            "connections_by_user": {
                user_id: len(connections) 
                for user_id, connections in self.user_connections.items()
            },
            "connections_by_session": {
                session_id: len(connections)
                for session_id, connections in self.session_connections.items()
            }
        }


# 创建全局连接管理器实例
websocket_manager = ConnectionManager()
