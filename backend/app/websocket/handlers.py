"""
WebSocket消息处理器

处理WebSocket消息和事件
"""

import json
import uuid
from typing import Dict, Any
from fastapi import WebSocket, WebSocketDisconnect, Query
import structlog

from app.websocket.manager import websocket_manager

logger = structlog.get_logger(__name__)


async def websocket_endpoint(
    websocket: WebSocket,
    user_id: str = Query(None, description="用户ID"),
    session_id: str = Query(None, description="会话ID")
):
    """
    WebSocket端点处理器
    """
    connection_id = str(uuid.uuid4())
    
    try:
        # 建立连接
        await websocket_manager.connect(
            websocket=websocket,
            connection_id=connection_id,
            user_id=user_id,
            session_id=session_id
        )
        
        # 发送连接确认消息
        await websocket_manager.send_personal_message({
            "type": "connection_established",
            "connection_id": connection_id,
            "message": "WebSocket connection established successfully"
        }, connection_id)
        
        # 消息处理循环
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理消息
                await handle_message(connection_id, message)
                
            except WebSocketDisconnect:
                logger.info("WebSocket disconnected", connection_id=connection_id)
                break
            except json.JSONDecodeError as e:
                logger.error(
                    "Invalid JSON received",
                    connection_id=connection_id,
                    error=str(e)
                )
                await websocket_manager.send_personal_message({
                    "type": "error",
                    "message": "Invalid JSON format"
                }, connection_id)
            except Exception as e:
                logger.error(
                    "Error processing WebSocket message",
                    connection_id=connection_id,
                    error=str(e),
                    exc_info=True
                )
                await websocket_manager.send_personal_message({
                    "type": "error",
                    "message": "Internal server error"
                }, connection_id)
    
    except Exception as e:
        logger.error(
            "WebSocket connection error",
            connection_id=connection_id,
            error=str(e),
            exc_info=True
        )
    
    finally:
        # 断开连接
        await websocket_manager.disconnect(connection_id)


async def handle_message(connection_id: str, message: Dict[str, Any]):
    """
    处理WebSocket消息
    """
    message_type = message.get("type")
    
    logger.debug(
        "Processing WebSocket message",
        connection_id=connection_id,
        message_type=message_type
    )
    
    # 根据消息类型分发处理
    if message_type == "ping":
        await handle_ping(connection_id, message)
    elif message_type == "pong":
        await handle_pong(connection_id, message)
    elif message_type == "subscribe":
        await handle_subscribe(connection_id, message)
    elif message_type == "unsubscribe":
        await handle_unsubscribe(connection_id, message)
    elif message_type == "dispatch_update":
        await handle_dispatch_update(connection_id, message)
    elif message_type == "fire_incident_update":
        await handle_fire_incident_update(connection_id, message)
    elif message_type == "resource_status_update":
        await handle_resource_status_update(connection_id, message)
    else:
        logger.warning(
            "Unknown message type",
            connection_id=connection_id,
            message_type=message_type
        )
        await websocket_manager.send_personal_message({
            "type": "error",
            "message": f"Unknown message type: {message_type}"
        }, connection_id)


async def handle_ping(connection_id: str, message: Dict[str, Any]):
    """处理ping消息"""
    await websocket_manager.send_personal_message({
        "type": "pong",
        "timestamp": message.get("timestamp")
    }, connection_id)


async def handle_pong(connection_id: str, message: Dict[str, Any]):
    """处理pong消息"""
    await websocket_manager.handle_pong(connection_id)


async def handle_subscribe(connection_id: str, message: Dict[str, Any]):
    """处理订阅消息"""
    topic = message.get("topic")
    
    if not topic:
        await websocket_manager.send_personal_message({
            "type": "error",
            "message": "Topic is required for subscription"
        }, connection_id)
        return
    
    # 这里可以实现订阅逻辑
    # 例如：将连接添加到特定主题的订阅列表
    
    logger.info(
        "Client subscribed to topic",
        connection_id=connection_id,
        topic=topic
    )
    
    await websocket_manager.send_personal_message({
        "type": "subscription_confirmed",
        "topic": topic,
        "message": f"Successfully subscribed to {topic}"
    }, connection_id)


async def handle_unsubscribe(connection_id: str, message: Dict[str, Any]):
    """处理取消订阅消息"""
    topic = message.get("topic")
    
    if not topic:
        await websocket_manager.send_personal_message({
            "type": "error",
            "message": "Topic is required for unsubscription"
        }, connection_id)
        return
    
    # 这里可以实现取消订阅逻辑
    
    logger.info(
        "Client unsubscribed from topic",
        connection_id=connection_id,
        topic=topic
    )
    
    await websocket_manager.send_personal_message({
        "type": "unsubscription_confirmed",
        "topic": topic,
        "message": f"Successfully unsubscribed from {topic}"
    }, connection_id)


async def handle_dispatch_update(connection_id: str, message: Dict[str, Any]):
    """处理调度更新消息"""
    session_id = message.get("session_id")
    update_data = message.get("data", {})
    
    if not session_id:
        await websocket_manager.send_personal_message({
            "type": "error",
            "message": "Session ID is required for dispatch updates"
        }, connection_id)
        return
    
    # 广播调度更新到相关会话的所有连接
    await websocket_manager.send_to_session({
        "type": "dispatch_progress",
        "session_id": session_id,
        "data": update_data
    }, session_id)
    
    logger.info(
        "Dispatch update broadcasted",
        connection_id=connection_id,
        session_id=session_id
    )


async def handle_fire_incident_update(connection_id: str, message: Dict[str, Any]):
    """处理火情事件更新消息"""
    incident_id = message.get("incident_id")
    update_data = message.get("data", {})
    
    if not incident_id:
        await websocket_manager.send_personal_message({
            "type": "error",
            "message": "Incident ID is required for fire incident updates"
        }, connection_id)
        return
    
    # 广播火情更新到所有连接
    await websocket_manager.broadcast({
        "type": "fire_incident_update",
        "incident_id": incident_id,
        "data": update_data
    })
    
    logger.info(
        "Fire incident update broadcasted",
        connection_id=connection_id,
        incident_id=incident_id
    )


async def handle_resource_status_update(connection_id: str, message: Dict[str, Any]):
    """处理资源状态更新消息"""
    resource_id = message.get("resource_id")
    status = message.get("status")
    
    if not resource_id or not status:
        await websocket_manager.send_personal_message({
            "type": "error",
            "message": "Resource ID and status are required"
        }, connection_id)
        return
    
    # 广播资源状态更新
    await websocket_manager.broadcast({
        "type": "resource_status_update",
        "resource_id": resource_id,
        "status": status,
        "updated_by": connection_id
    })
    
    logger.info(
        "Resource status update broadcasted",
        connection_id=connection_id,
        resource_id=resource_id,
        status=status
    )
