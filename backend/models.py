"""
数据模型定义

基于现有数据库结构定义的消防站相关模型
"""

from typing import Optional, List
from pydantic import BaseModel, Field
from datetime import datetime


class FireFulltimeStation(BaseModel):
    """专职消防站模型 - 对应 fire_fulltime 表"""
    
    id: str = Field(description="站点ID")
    dadui: Optional[str] = Field(default=None, description="所属大队")
    name: Optional[str] = Field(default=None, description="站点名称")
    address: Optional[str] = Field(default=None, description="地址")
    statue: Optional[str] = Field(default=None, description="状态")
    horizontal: Optional[float] = Field(default=None, description="经度")
    vertical: Optional[float] = Field(default=None, description="纬度")
    rank: Optional[int] = Field(default=None, description="排序")
    
    # 基本信息
    jyz_name: Optional[str] = Field(default=None, description="救援站名称")
    img_url: Optional[str] = Field(default=None, description="图片URL")


class FireWorkstation(BaseModel):
    """消防工作站模型 - 对应 fire_workstation 表"""
    
    id: int = Field(description="工作站ID")
    region: Optional[str] = Field(default=None, description="区域")
    jiedao: Optional[str] = Field(default=None, description="街道")
    name: Optional[str] = Field(default=None, description="工作站名称")
    address: Optional[str] = Field(default=None, description="地址")
    horizontal: Optional[float] = Field(default=None, description="经度")
    vertical: Optional[float] = Field(default=None, description="纬度")
    rank: Optional[int] = Field(default=None, description="排序")
    dadui: Optional[str] = Field(default=None, description="所属大队")
    img_url: Optional[str] = Field(default=None, description="图片URL")


class FireStationLocation(BaseModel):
    """消防站位置信息（统一格式）"""
    
    id: str = Field(description="站点ID")
    name: str = Field(description="站点名称")
    address: Optional[str] = Field(default=None, description="地址")
    latitude: float = Field(description="纬度")
    longitude: float = Field(description="经度")
    station_type: str = Field(description="站点类型", examples=["fulltime", "workstation"])
    dadui: Optional[str] = Field(default=None, description="所属大队")
    status: Optional[str] = Field(default=None, description="状态")
    img_url: Optional[str] = Field(default=None, description="图片URL")


class FireStationSearchRequest(BaseModel):
    """消防站搜索请求"""
    
    latitude: float = Field(description="搜索中心纬度")
    longitude: float = Field(description="搜索中心经度")
    radius: float = Field(default=5000, description="搜索半径（米）")
    station_types: Optional[List[str]] = Field(default=None, description="站点类型筛选")
    dadui_filter: Optional[str] = Field(default=None, description="大队筛选")
    status_filter: Optional[str] = Field(default=None, description="状态筛选")


class FireStationSearchResponse(BaseModel):
    """消防站搜索响应"""
    
    total_count: int = Field(description="总数量")
    stations: List[FireStationLocation] = Field(description="站点列表")
    search_center: dict = Field(description="搜索中心")
    search_radius: float = Field(description="搜索半径")


# API响应模型
class ResourceResponse(BaseModel):
    """资源响应模型"""
    id: str = Field(description="资源ID")
    type: str = Field(description="资源类型")
    name: str = Field(description="资源名称")
    address: Optional[str] = Field(default=None, description="地址")
    latitude: float = Field(description="纬度")
    longitude: float = Field(description="经度")
    dadui: Optional[str] = Field(default=None, description="所属大队")
    status: Optional[str] = Field(default=None, description="状态")
    distance: Optional[float] = Field(default=None, description="距离查询点的距离（米）")
    img_url: Optional[str] = Field(default=None, description="图片URL")


class ResourceQuery(BaseModel):
    """资源查询请求模型"""
    latitude: float = Field(description="查询中心纬度")
    longitude: float = Field(description="查询中心经度")
    radius: float = Field(default=5000, description="查询半径（米）")
    station_types: Optional[List[str]] = Field(default=None, description="站点类型筛选")
    dadui_filter: Optional[str] = Field(default=None, description="大队筛选")
    status_filter: Optional[str] = Field(default=None, description="状态筛选")


# 火情事件相关模型
class FireLocation(BaseModel):
    """火情位置信息"""
    latitude: float = Field(description="纬度")
    longitude: float = Field(description="经度")
    address: str = Field(description="详细地址")
    district: Optional[str] = Field(default=None, description="所属辖区")


class FireIncident(BaseModel):
    """火情事件信息"""
    id: Optional[str] = Field(default=None, description="事件ID")
    location: FireLocation = Field(description="火情位置")
    severity: str = Field(description="严重程度")
    fire_type: str = Field(description="火灾类型")
    description: str = Field(description="火情描述")
    reported_time: datetime = Field(description="上报时间")
    estimated_area: Optional[float] = Field(default=None, description="预估面积（平方米）")
    wind_direction: Optional[str] = Field(default=None, description="风向")
    wind_speed: Optional[float] = Field(default=None, description="风速（m/s）")
    weather_conditions: Optional[str] = Field(default=None, description="天气条件")


class FireIncidentCreate(BaseModel):
    """创建火情事件请求模型"""
    location: FireLocation = Field(description="火情位置")
    severity: str = Field(description="严重程度")
    fire_type: str = Field(description="火灾类型")
    description: str = Field(description="火情描述")
    estimated_area: Optional[float] = Field(default=None, description="预估面积")
    wind_direction: Optional[str] = Field(default=None, description="风向")
    wind_speed: Optional[float] = Field(default=None, description="风速")
    weather_conditions: Optional[str] = Field(default=None, description="天气条件")


class FireIncidentResponse(BaseModel):
    """火情事件响应模型"""
    id: str = Field(description="事件ID")
    location: FireLocation = Field(description="火情位置")
    severity: str = Field(description="严重程度")
    fire_type: str = Field(description="火灾类型")
    description: str = Field(description="火情描述")
    reported_time: datetime = Field(description="上报时间")
    status: str = Field(description="事件状态")
