"""
API测试脚本

测试消防站API的基本功能
"""

import asyncio
import httpx
import json
from typing import Dict, Any


class APITester:
    """API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = None
    
    async def __aenter__(self):
        self.client = httpx.AsyncClient(base_url=self.base_url, timeout=30.0)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.aclose()
    
    async def test_health(self):
        """测试健康检查"""
        print("🔍 测试健康检查...")
        try:
            response = await self.client.get("/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查通过: {data['status']}")
                print(f"   服务: {data['service']}")
                print(f"   版本: {data['version']}")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    async def test_get_dadui_list(self):
        """测试获取大队列表"""
        print("\n🔍 测试获取大队列表...")
        try:
            response = await self.client.get("/api/v1/resources/dadui/list")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 获取大队列表成功: 共{data['total_count']}个大队")
                for dadui in data['dadui_list'][:5]:  # 只显示前5个
                    print(f"   - {dadui}")
                if len(data['dadui_list']) > 5:
                    print(f"   ... 还有{len(data['dadui_list']) - 5}个")
                return True
            else:
                print(f"❌ 获取大队列表失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 获取大队列表异常: {e}")
            return False
    
    async def test_get_statistics(self):
        """测试获取统计信息"""
        print("\n🔍 测试获取统计信息...")
        try:
            response = await self.client.get("/api/v1/resources/statistics/summary")
            if response.status_code == 200:
                data = response.json()
                print("✅ 获取统计信息成功:")
                print(f"   总站点数: {data['total_stations']}")
                print(f"   专职消防站: {data['fulltime_stations']['total']}")
                print(f"   消防工作站: {data['workstations']['total']}")
                return True
            else:
                print(f"❌ 获取统计信息失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 获取统计信息异常: {e}")
            return False
    
    async def test_list_resources(self):
        """测试获取资源列表"""
        print("\n🔍 测试获取资源列表...")
        try:
            response = await self.client.get("/api/v1/resources/?limit=5")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 获取资源列表成功: 共{len(data)}个资源")
                for resource in data:
                    print(f"   - {resource['name']} ({resource['type']}) - {resource['dadui']}")
                return True
            else:
                print(f"❌ 获取资源列表失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 获取资源列表异常: {e}")
            return False
    
    async def test_search_resources(self):
        """测试搜索资源"""
        print("\n🔍 测试搜索资源...")
        try:
            # 使用北京市中心坐标进行搜索
            search_data = {
                "latitude": 39.9042,
                "longitude": 116.4074,
                "radius": 10000,  # 10公里
                "station_types": ["fulltime", "workstation"]
            }
            
            response = await self.client.post("/api/v1/resources/search", json=search_data)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 搜索资源成功: 找到{len(data)}个资源")
                for resource in data[:3]:  # 只显示前3个
                    print(f"   - {resource['name']} ({resource['type']}) - 距离: {resource['distance']:.0f}米")
                return True
            else:
                print(f"❌ 搜索资源失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 搜索资源异常: {e}")
            return False
    
    async def test_get_station_types(self):
        """测试获取站点类型"""
        print("\n🔍 测试获取站点类型...")
        try:
            response = await self.client.get("/api/v1/resources/types/list")
            if response.status_code == 200:
                data = response.json()
                print("✅ 获取站点类型成功:")
                for station_type in data['station_types']:
                    print(f"   - {station_type['type']}: {station_type['name']} - {station_type['description']}")
                return True
            else:
                print(f"❌ 获取站点类型失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 获取站点类型异常: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始API测试...")
        print("=" * 60)
        
        tests = [
            self.test_health,
            self.test_get_dadui_list,
            self.test_get_statistics,
            self.test_get_station_types,
            self.test_list_resources,
            self.test_search_resources
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if await test():
                    passed += 1
            except Exception as e:
                print(f"❌ 测试异常: {e}")
        
        print("\n" + "=" * 60)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！")
        else:
            print("⚠️  部分测试失败，请检查服务状态")
        
        return passed == total


async def main():
    """主函数"""
    print("🔥 消防站API测试工具")
    print("确保服务已启动: python start_server.py")
    print()
    
    async with APITester() as tester:
        success = await tester.run_all_tests()
        return success


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        exit(1)
